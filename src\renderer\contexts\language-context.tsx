import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// 支持的语言类型
export type Language = 'zh-CN' | 'en-US'

// 语言上下文类型
interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string, params?: Record<string, any>) => string
}

// 翻译文本
const translations = {
  'zh-CN': {
    // 应用标题
    'app.title': 'ArroEngine',
    'app.subtitle': '智能文件整理助手',
    
    // 导航菜单
    'nav.workspace': '工作区',
    'nav.rules': '规则中心',
    'nav.monitoring': '监控',
    'nav.history': '历史记录',
    
    // 设置界面
    'settings.title': '设置',
    'settings.subtitle': '自定义应用程序设置',
    'settings.general': '常规设置',
    'settings.appearance': '外观',
    'settings.workflowProcessing': '处理管理',
    'settings.history': '历史记录',
    'settings.storage': '存储管理',
    'settings.about': '关于',
    'settings.language': '界面语言',
    'settings.selectLanguage': '选择语言',
    'settings.selectLanguage.desc': '更改应用程序显示语言',
    'settings.theme': '主题',
    'settings.theme.dark': '深色主题',
    'settings.theme.light': '浅色主题',
    'settings.uiScale': '界面缩放',
    'settings.resetWindowSize': '恢复默认尺寸',
    'settings.resetWindowSize.desc': '将窗口大小重置为默认比例',
    'settings.resetWindowSize.success': '窗口大小已重置为默认值',
    'settings.resetWindowSize.failed': '重置窗口大小失败',
    'settings.minimizeToTray': '最小化到系统托盘',
    'settings.minimizeToTray.desc': '关闭窗口时最小化到系统托盘而不是退出',
    'settings.autoLaunch': '开机自启动',
    'settings.autoLaunch.desc': '系统启动时自动启动应用程序',
    'settings.autoLaunchEnabled': '开机自启动已启用',
    'settings.autoLaunchDisabled': '开机自启动已禁用',
    'settings.saved': '设置已保存',
    'settings.savedDesc': '您的设置已成功保存',
    
    // 系统托盘
    'tray.hideWindow': '隐藏窗口',
    'tray.showWindow': '显示窗口',
    'tray.enableAutoLaunch': '启用开机自启',
    'tray.disableAutoLaunch': '禁用开机自启',
    'tray.exit': '退出',
    
    // 常规设置
    'settings.autoStart': '开机自启动',
    'settings.autoStart.desc': '系统启动时自动运行ArroEngine',
    
    // 历史记录设置
    'settings.maxHistoryEntries': '最大历史记录数量',
    'settings.maxHistoryEntries.desc': '超过此数量时自动删除最旧的记录',
    'settings.autoCleanupDays': '自动清理天数',
    'settings.autoCleanupDays.desc': '自动删除超过指定天数的历史记录',

    // 工作流处理设置
    'settings.workflowProcessing.maxItems': '处理上限',
    'settings.workflowProcessing.maxItems.desc': '单次处理的最大文件/文件夹数量',
    'settings.workflowProcessing.batchSize': '批处理大小',
    'settings.workflowProcessing.batchSize.desc': '每批处理的文件数量，避免性能问题',
    'settings.workflowProcessing.batchInterval': '处理间隔',
    'settings.workflowProcessing.batchInterval.desc': '批次之间的等待时间，单位毫秒',

    'settings.clearAllHistory': '清空所有历史记录',
    'settings.clearAllHistory.desc': '此操作不可撤销，请谨慎操作',
    'settings.history.totalEntries': '历史记录数',
    'settings.history.totalFiles': '处理的文件',
    'settings.history.activeDays': '活跃天数',
    'settings.history.confirmClear': '确认清空历史记录',
    'settings.history.confirmClearDesc': '此操作将删除所有历史记录，且不可恢复。确定要继续吗？',
    'settings.history.confirmYes': '是，清空记录',
    'settings.history.confirmNo': '取消',
    'settings.history.clearSuccess': '历史记录已清空',
    'settings.save': '保存',

    // 存储管理
    'settings.appData': '应用数据',
    'settings.historyData': '历史记录',
    'settings.tempFiles': '临时文件',
    'settings.cleanTempFiles': '清理临时文件',
    'settings.cleanTempFiles.desc': '清理应用产生的临时文件和缓存',
    'settings.cleaning': '清理中...',
    'settings.refreshData': '刷新数据',
    'settings.storageUsage': '存储使用情况',
    
    // 关于
    'settings.about.subtitle': '智能文件整理工具',
    'settings.about.version': '版本',
    'settings.about.author': '作者',
    'settings.about.authorName': 'ZKZ',
    'settings.about.features': '功能特性',
    'settings.about.feature1': '智能文件分类和整理',
    'settings.about.feature2': '实时文件监控',
    'settings.about.feature3': '定时任务处理',
    'settings.about.feature4': '操作历史记录和撤销',
    'settings.about.feature5': '可视化规则编辑',
    'settings.about.checkUpdate': '检查更新',
    'settings.about.checkUpdate.desc': '功能开发中',
    'settings.about.updateNotice': '检查更新功能正在开发中，敬请期待！',
    
    // 语言选项
    'language.zh-CN': '简体中文',
    'language.en-US': 'English',

    // 监控界面
    'monitor.title': '监控中心',
    'monitor.description': '管理文件监控和定时任务',
    'monitor.createTask': '创建监控任务',
    'monitor.noTasks': '暂无监控任务',
    'monitor.createFirst': '创建你的第一个监控任务',
    'monitor.enable': '启用',
    'monitor.disable': '禁用',
    'monitor.edit': '编辑',
    'monitor.delete': '删除',
    'monitor.execute': '立即执行',
    'monitor.details': '查看详情',
    'monitor.status.running': '运行中',
    'monitor.status.stopped': '已停止',
    'monitor.status.error': '错误',

    // 通用按钮和操作
    'common.save': '保存',
    'common.cancel': '取消',
    'common.delete': '删除',
    'common.edit': '编辑',
    'common.create': '创建',
    'common.import': '导入',
    'common.export': '导出',
    'common.confirm': '确认',
    'common.dismiss': '忽略',
    'common.validationError': '验证错误',
    'common.close': '关闭',
    'common.files': '文件',
    'common.folders': '文件夹',
    'common.back': '返回',
    'common.next': '下一步',
    'common.previous': '上一步',
    'common.finish': '完成',
    'common.loading': '加载中...',
    'common.error': '错误',
    'common.success': '成功',
    'common.warning': '警告',
    'common.info': '信息',
    'common.yes': '是',
    'common.no': '否',

    // 监控任务相关
    'monitor.task.name': '任务名称',
    'monitor.task.type': '任务类型',
    'monitor.task.rule': '关联规则',
    'monitor.task.status': '任务状态',
    'monitor.task.lastExecution': '最后执行',
    'monitor.task.executionCount': '执行次数',
    'monitor.task.successRate': '成功率',
    'monitor.task.fileWatch': '文件监控',
    'monitor.task.scheduled': '定时任务',
    'monitor.task.enabled': '已启用',
    'monitor.task.disabled': '已禁用',
    'monitor.task.running': '运行中',
    'monitor.task.stopped': '已停止',
    'monitor.task.error': '错误',
    'monitor.task.never': '从未执行',
    'monitor.task.idle': '空闲',
    'monitor.task.waiting': '等待中',

    // 监控统计
    'monitor.stats.totalTasks': '总任务数',
    'monitor.stats.runningTasks': '运行中',
    'monitor.stats.disabledTasks': '已禁用',
    'monitor.stats.errorTasks': '错误',

    // 监控任务创建/编辑
    'monitor.create.title': '创建监控任务',
    'monitor.edit.title': '编辑监控任务',
    'monitor.create.taskName': '任务名称',
    'monitor.create.selectType': '选择任务类型',
    'monitor.create.fileWatch.title': '文件监控',
    'monitor.create.fileWatch.desc': '监控指定文件夹的文件变化',
    'monitor.create.scheduled.title': '定时任务',
    'monitor.create.scheduled.desc': '按照设定的时间间隔执行',
    'monitor.create.selectRule': '选择规则',
    'monitor.create.selectRule.placeholder': '请选择要执行的规则',
    'monitor.create.watchPaths': '监控路径',
    'monitor.create.addPath': '添加路径',
    'monitor.create.removePath': '移除路径',
    'monitor.create.browsePath': '浏览',
    'monitor.create.enableAutoExecution': '启用自动执行',
    'monitor.create.batchSize': '批处理大小',
    'monitor.create.batchSize.desc': '每批处理的文件数量',
    'monitor.create.debounceDelay': '处理间隔',
    'monitor.create.debounceDelay.desc': '文件变化后等待时间（毫秒）',
    'monitor.create.schedule': '执行计划',
    'monitor.create.schedule.placeholder': '请输入cron表达式',
    'monitor.create.schedule.examples': '示例：0 */1 * * * （每小时执行一次）',

    // 监控任务创建/编辑 - 新增翻译键
    'monitor.create.fillRequired': '请填写任务名称并选择规则',
    'monitor.create.fillWatchPath': '请填写监控路径',
    'monitor.create.fillInputPath': '请填写输入路径',
    'monitor.create.fillCronExpression': '请填写Cron表达式',
    'monitor.create.basicInfo': '基本信息',
    'monitor.create.taskNamePlaceholder': '输入任务名称',
    'monitor.create.createTask': '创建任务',
    'monitor.create.taskDescription': '任务描述',
    'monitor.create.taskDescriptionPlaceholder': '输入任务描述（可选）',
    'monitor.create.taskType': '任务类型',
    'monitor.create.fileWatch': '文件监控',
    'monitor.create.scheduled': '定时任务',
    'monitor.create.associatedRule': '关联规则',
    'monitor.create.fileWatchConfig': '文件监控配置',
    'monitor.create.watchPath': '监控路径',
    'monitor.create.watchPathPlaceholder': '选择要监控的文件夹',
    'monitor.create.addWatchPath': '添加监控路径',
    'monitor.create.includeSubfolders': '包含子文件夹',
    'monitor.create.ignorePatterns': '忽略模式',
    'monitor.create.ignorePatternPlaceholder': '例如: *.tmp 或 node_modules',
    'monitor.create.ignorePatternsDesc': '设置要忽略的文件或文件夹',
    'monitor.create.addIgnorePattern': '+ 添加忽略模式',
    'monitor.create.availablePatternsTitle': '实用忽略模式：',
    'monitor.create.patternExamplesTitle': '使用场景示例：',
    'monitor.create.pattern.specificFile': '重要文档.docx - 忽略特定文件',
    'monitor.create.pattern.specificFolder': '重要资料 - 忽略特定文件夹',
    'monitor.create.pattern.fileType': '*.psd - 忽略所有PSD设计文件',
    'monitor.create.pattern.multipleTypes': '*.{exe,msi} - 忽略所有安装程序',
    'monitor.create.pattern.folderContents': '私人文件/* - 忽略文件夹内所有内容',
    'monitor.create.pattern.namePattern': '*备份* - 忽略文件名包含"备份"的文件',
    'monitor.create.pattern.systemFiles': 'desktop.ini - 忽略系统配置文件',
    'monitor.create.pattern.hiddenFiles': '.* - 忽略所有隐藏文件',
    'monitor.create.pattern.tempFiles': '*~* - 忽略临时编辑文件',
    'monitor.create.pattern.cacheFiles': 'Thumbs.db - 忽略缩略图缓存',
    'monitor.create.scenarioTitle': '使用场景：',
    'monitor.create.scenario.specificFile': '忽略特定文件：',
    'monitor.create.scenario.specificFolder': '忽略特定文件夹：',
    'monitor.create.scenario.fileTypes': '忽略某类文件：',
    'monitor.create.scenario.nameContains': '忽略名称包含特定词的文件：',
    'monitor.create.wildcardRulesTitle': '通配符规则：',
    'monitor.create.wildcard.star': '* - 匹配任意字符',
    'monitor.create.wildcard.question': '? - 匹配单个字符',
    'monitor.create.wildcard.brackets': '\\{jpg,png\\} - 匹配多个扩展名',
    'monitor.create.wildcard.range': '[0-9] - 匹配数字范围',
    'monitor.create.wildcard.negation': '[!abc] - 不匹配指定字符',
    'monitor.create.watchEvents': '监控事件',
    'monitor.create.watchEventsDesc': '选择触发规则执行的文件事件。当监测到勾选的事件发生时，将自动执行关联的规则',
    'monitor.create.advancedSettings': '高级设置',
    'monitor.create.skipIfRunning': '任务运行时跳过',
    'monitor.create.skipIfRunningDesc': '如果上一次任务还在运行，则跳过本次执行，避免任务重叠',
    'monitor.create.eventAdd': '文件创建',
    'monitor.create.eventChange': '文件修改',
    'monitor.create.eventDelete': '文件删除',
    'monitor.create.eventAddDir': '文件夹创建',
    'monitor.create.eventDeleteDir': '文件夹删除',
    'monitor.create.commonPatterns': '常用忽略模式',
    'monitor.create.ignoreTempFiles': '临时文件',
    'monitor.create.ignoreLogFiles': '日志文件',
    'monitor.create.ignoreBackupFiles': '备份文件',
    'monitor.create.ignoreCacheFolder': '缓存文件夹',
    'monitor.create.ignoreTempFolder': '临时文件夹',
    'monitor.create.ignoreNodeModules': 'Node模块',
    'monitor.create.example.importantDoc': '重要文档.docx',
    'monitor.create.example.importantFiles': '重要资料',
    'monitor.create.example.privateFiles': '私人文件',
    'monitor.create.example.backup': '*备份*',
    'monitor.create.scheduledConfig': '定时任务配置',
    'monitor.create.inputPath': '输入路径',
    'monitor.create.inputPathPlaceholder': '选择要处理的文件夹',
    'monitor.create.scheduleTime': '执行时间',
    'monitor.create.daily': '每日',
    'monitor.create.hourly': '每小时',
    'monitor.create.weekly': '每周',
    'monitor.create.custom': '自定义',
    'monitor.create.executeTime': '执行时间',
    'monitor.create.recursiveProcess': '递归处理',

    // 监控任务详情
    'monitor.detail.basicInfo': '基本信息',
    'monitor.detail.expired': '已过期',
    'monitor.detail.aboutToRun': '即将执行',
    'monitor.detail.minutesLater': '{minutes}分钟后',
    'monitor.detail.hoursLater': '{hours}小时后',
    'monitor.detail.daysLater': '{days}天后',
    'monitor.detail.disabled': '已禁用',
    'monitor.detail.running': '运行中',
    'monitor.detail.error': '错误',
    'monitor.detail.waiting': '等待中',
    'monitor.detail.idle': '空闲',
    'monitor.detail.fileWatch': '文件监控',
    'monitor.detail.scheduled': '定时任务',
    'monitor.detail.executeNow': '立即执行',
    'monitor.detail.disableTask': '禁用任务',
    'monitor.detail.enableTask': '启用任务',
    'monitor.detail.editTask': '编辑任务',
    'monitor.detail.taskDescription': '任务描述：',
    'monitor.detail.noDescription': '无描述',
    'monitor.detail.associatedRule': '关联规则：',
    'monitor.detail.unknownRule': '未知规则',
    'monitor.detail.createdAt': '创建时间：',
    'monitor.detail.updatedAt': '更新时间：',
    'monitor.detail.lastExecuted': '上次执行：',
    'monitor.detail.nextExecution': '下次执行：',
    'monitor.detail.executionStats': '执行统计',
    'monitor.detail.totalExecutions': '总执行次数',
    'monitor.detail.successfulExecutions': '成功次数',
    'monitor.detail.failedExecutions': '失败次数',
    'monitor.detail.processedFiles': '处理文件数',
    'monitor.detail.averageExecutionTime': '平均执行时间：',
    'monitor.detail.lastExecutionDuration': '上次执行时长：',
    'monitor.detail.recentError': '最近错误',
    'monitor.detail.fileWatchConfig': '文件监控配置',
    'monitor.detail.scheduledConfig': '定时任务配置',
    'monitor.detail.watchPaths': '监控路径：',
    'monitor.detail.watchEvents': '监控事件：',
    'monitor.detail.includeSubfolders': '包含子文件夹：',
    'monitor.detail.debounceDelay': '处理间隔：',
    'monitor.detail.batchSize': '批处理大小：',
    'monitor.detail.unlimited': '无限制',
    'monitor.detail.inputPath': '输入路径：',
    'monitor.detail.cronExpression': 'Cron表达式：',
    'monitor.detail.timezone': '时区：',
    'monitor.detail.skipIfRunning': '运行时跳过：',
    'monitor.detail.totalFileCount': '总文件数',
    'monitor.detail.successCount': '成功处理',
    'monitor.detail.failedCount': '处理失败',
    'monitor.detail.duration': '执行时长',
    'monitor.detail.fileOperations': '文件操作详情',
    'monitor.detail.currentLocation': '当前位置：',
    'monitor.detail.originalLocation': '原来位置：',
    'monitor.detail.status': '状态',
    'monitor.detail.statusUndone': '已撤销到原位置',
    'monitor.detail.originalPath': '原路径：',
    'monitor.detail.newPath': '新路径：',
    'monitor.detail.operationType': '操作类型：',
    'monitor.detail.errorInfo': '错误信息：',
    'monitor.detail.statusSuccess': '成功',
    'monitor.detail.statusError': '失败',
    'monitor.detail.statusProcessing': '处理中',
    'monitor.detail.errorSummary': '错误汇总',
    'monitor.detail.step': '步骤: {name}',
    'monitor.detail.processLog': '处理日志',

    // 监控任务操作确认
    'monitor.deleteConfirmTitle': '删除监控任务',
    'monitor.deleteConfirmDesc': '确定要删除监控任务 "{name}" 吗？此操作不可撤销。',
    'monitor.executeFailedTitle': '任务执行遇到问题',
    'monitor.executeFailedDesc': '任务执行时出现了问题，请检查以下信息：\n\n{error}',
    'monitor.createFailedTitle': '任务创建失败',
    'monitor.createFailedDesc': '创建监控任务时出现问题：\n\n{error}',
    'monitor.updateFailedTitle': '任务更新失败',
    'monitor.updateFailedDesc': '更新监控任务时出现问题：\n\n{error}',

    // 历史记录详细
    'history.details.title': '历史详情',
    'history.details.executionInfo': '执行信息',
    'history.details.fileOperations': '文件操作',
    'history.details.errors': '错误信息',
    'history.details.duration': '执行时长',
    'history.details.startTime': '开始时间',
    'history.details.endTime': '结束时间',
    'history.details.totalFiles': '总文件数',
    'history.details.processedFiles': '已处理文件数',
    'history.details.operation.move': '移动',
    'history.details.operation.copy': '复制',
    'history.details.operation.rename': '重命名',
    'history.details.operation.delete': '删除',
    'history.details.operation.cleanup_empty_folder': '清理空文件夹',
    'history.details.originalPath': '原路径',
    'history.details.newPath': '新路径',
    'history.details.noOperations': '无文件操作',
    'history.details.noErrors': '无错误',

    // 确认对话框
    'confirm.delete.title': '确认删除',
    'confirm.delete.message': '确定要删除这个项目吗？此操作不可撤销。',
    'confirm.clearHistory.title': '确认清空',
    'confirm.clearHistory.message': '确定要清空所有历史记录吗？此操作不可撤销。',
    'confirm.undo.title': '确认撤销',
    'confirm.undo.message': '确定要撤销这个操作吗？',
    'confirm.default.confirm': '确定',
    'confirm.default.cancel': '取消',

    // 规则中心界面
    'rule.center.title': '规则中心',
    'rule.center.resetDefault': '重置默认',
    'rule.center.createRule': '新建规则',
    'rule.center.loading': '加载中...',
    'rule.center.noRules': '暂无规则',
    'rule.center.noRulesDesc': '点击"重置默认"加载预设规则',
    'rule.center.selectRule': '选择规则',
    'rule.center.selectRuleDesc': '从左侧选择一个规则查看详情',
    'rule.center.newRule': '新建规则',
    'rule.center.newRuleDesc': '请编辑规则描述',
    'rule.center.createFailed': '创建失败',
    'rule.center.createFailedDesc': '创建规则失败，请重试',
    'rule.center.saveFailed': '保存失败',
    'rule.center.saveFailedDesc': '保存失败，请重试',
    'rule.center.saveSuccess': '保存成功',
    'rule.center.saveSuccessDesc': '步骤保存成功！',
    'rule.center.resetTitle': '重置默认规则',
    'rule.center.resetDesc': '确定要重置默认规则吗？这将恢复或添加缺失的默认规则，不会影响您创建的自定义规则。',
    'rule.center.resetConfirm': '重置',
    'rule.center.deleteRule': '删除规则',
    'rule.center.deleteRuleDesc': '确定要删除规则"{name}"吗？此操作将删除规则及其所有步骤，无法撤销。',
    'rule.center.deleteStep': '删除步骤',
    'rule.center.deleteStepDesc': '确定要删除步骤"{name}"吗？此操作无法撤销。',
    'rule.center.stepsCount': '{count} 个步骤',
    'rule.center.enabled': '已启用',
    'rule.center.disabled': '已禁用',
    'rule.center.enable': '启用',
    'rule.center.disable': '禁用',
    'rule.center.edit': '编辑',
    'rule.center.delete': '删除',
    'rule.center.addStep': '添加步骤',
    'rule.center.processSteps': '处理步骤',
    'rule.center.autoCleanupEnabled': '清理处理后的空文件夹',
    'rule.center.autoCleanupDisabled': '保留处理后的空文件夹',
    'rule.center.includeSubfoldersEnabled': '包含子文件夹',
    'rule.center.includeSubfoldersDisabled': '不包含子文件夹',
    'rule.center.goToWorkspace': '去工作区测试',
    'rule.center.doubleClickEditName': '双击编辑名称',
    'rule.center.doubleClickEditDesc': '双击编辑描述',
    'rule.center.nameMaxLength': '最多{max}个字符',
    'rule.center.descMaxLength': '最多{max}个字符',
    'rule.center.deleteRuleTooltip': '删除规则',
    'rule.center.dragHandle': '拖拽手柄',
    'rule.center.enableDisableButton': '启用/禁用按钮',
    'rule.center.editButton': '编辑按钮',
    'rule.center.deleteButton': '删除按钮',
    'rule.center.filterConditions': '筛选条件: {count} 个',
    'rule.center.processActions': '处理动作: {count} 个',
    'rule.center.processTarget': '处理对象',
    'rule.center.processTarget.files': '文件',
    'rule.center.processTarget.folders': '文件夹',
    'rule.center.inputSource': '输入源',
    'rule.center.inputSource.original': '初始目录',
    'rule.center.inputSource.previousStep': '上一步骤输出',
    'rule.center.inputSource.specificPath': '指定路径',
    'rule.center.conditions': '条件',
    'rule.center.actions': '动作',
    'rule.center.actionDetails': '动作详情',
    'rule.center.conditionCount': '个条件',
    'rule.center.actionCount': '个动作',
    'rule.center.noConditions': '无条件限制',
    'rule.center.noActions': '未配置动作',
    'rule.center.notConfigured': '未配置',
    'rule.center.moreActions': '个更多动作',
    'rule.center.moreConditions': '个更多条件',
    'rule.center.condition': '条件',
    'rule.center.action': '动作',
    'rule.center.andMore': '还有{{count}}个',
    'rule.center.actionType.move': '移动',
    'rule.center.actionType.copy': '复制',
    'rule.center.actionType.rename': '重命名',
    'rule.center.actionType.delete': '删除',
    'rule.center.stepName': '步骤 {number}',
    'rule.center.stepDesc': '描述这个步骤的功能',
    'rule.center.unknownRule': '未知规则',
    'rule.center.unknownStep': '未知步骤',

    // 悬浮步骤编辑器
    'stepEditor.title': '编辑步骤: {name}',
    'stepEditor.rule': '规则: {name}',
    'stepEditor.save': '保存',
    'stepEditor.cancel': '取消',
    'stepEditor.stepName': '步骤名称',
    'stepEditor.stepNamePlaceholder': '输入步骤名称',
    'stepEditor.stepDesc': '步骤描述',
    'stepEditor.stepDescPlaceholder': '描述步骤功能',
    'stepEditor.inputSource': '输入来源',
    'stepEditor.inputSource.original': '初始目录',
    'stepEditor.inputSource.previousStep': '指定步骤输出',
    'stepEditor.inputSource.specificPath': '指定路径',
    'stepEditor.targetPath': '输入路径',
    'stepEditor.targetPathPlaceholder': '选择或输入输入路径',
    'stepEditor.specificPathDesc': '此步骤将从指定路径重新加载文件，而不是使用前面步骤的输出',
    'stepEditor.originalDesc': '使用工作流当前状态的文件（跳过被禁用的步骤）',
    'stepEditor.previousStepDesc': '使用指定步骤的输出文件（支持分支处理）',
    'stepEditor.selectStep': '选择步骤',
    'stepEditor.selectStepPlaceholder': '选择要使用输出的步骤',
    'stepEditor.lastStep': '上一个步骤',
    'stepEditor.step': '步骤',
    'stepEditor.specificPathRequired': '使用特定路径输入源时必须指定路径',
    'stepEditor.browse': '浏览',
    'stepEditor.filterConditions': '筛选条件',
    'stepEditor.filterConditionsDesc': '设置文件筛选规则，只有满足条件的文件才会被处理',
    'stepEditor.processActions': '处理动作',
    'stepEditor.processActionsDesc': '定义对满足条件的文件执行的操作，会按顺序执行',
    'stepEditor.basicSettings': '基本设置',
    'stepEditor.basicSettingsDesc': '配置步骤的基本信息和处理对象',
    'stepEditor.processTarget': '处理对象',
    'stepEditor.processTarget.files': '文件',
    'stepEditor.processTarget.filesDesc': '处理文件夹中的文件',
    'stepEditor.processTarget.folders': '文件夹',
    'stepEditor.processTarget.foldersDesc': '处理文件夹本身',

    'stepEditor.required': '必选',
    'stepEditor.processTargetRequired': '请选择要处理的对象类型，这将决定可用的筛选条件和处理动作。',
    'stepEditor.inputSourceTitle': '输入来源',
    'stepEditor.tabBasic': '基本\n设置',
    'stepEditor.tabConditions': '筛选\n条件',
    'stepEditor.tabActions': '处理\n动作',

    // 步骤编辑器验证
    'stepEditor.validation.targetPathRequired': '请为移动、复制或创建文件夹动作设置输出路径',
    'stepEditor.validation.processTargetRequired': '请选择处理对象（文件或文件夹）',
    'stepEditor.validation.actionsRequired': '请至少添加一个处理动作',

    // 条件编辑器
    'condition.field.fileName': '文件名',
    'condition.field.fileExtension': '文件扩展名',
    'condition.field.fileSize': '文件大小',
    'condition.field.fileType': '文件类型',
    'condition.field.createdDate': '创建日期',
    'condition.field.modifiedDate': '修改日期',
    'condition.field.filePath': '文件路径',
    'condition.field.folderName': '文件夹名称',
    'condition.field.folderSize': '文件夹大小',
    'condition.field.folderFileCount': '文件数量',
    'condition.field.folderSubfolderCount': '子文件夹数量',
    'condition.field.folderIsEmpty': '是否为空',
    'condition.field.itemType': '项目类型',
    'condition.operator.contains': '包含',
    'condition.operator.notContains': '不包含',
    'condition.operator.equals': '等于',
    'condition.operator.notEquals': '不等于',
    'condition.operator.is': '是',
    'condition.operator.startsWith': '开头是',
    'condition.operator.notStartsWith': '开头不是',
    'condition.operator.endsWith': '结尾是',
    'condition.operator.notEndsWith': '结尾不是',
    'condition.operator.regex': '正则匹配',
    'condition.operator.greaterThan': '大于',
    'condition.operator.lessThan': '小于',
    'condition.operator.greaterThanOrEqual': '大于等于',
    'condition.operator.lessThanOrEqual': '小于等于',
    'condition.operator.laterThan': '晚于',
    'condition.operator.earlierThan': '早于',
    'condition.operator.notEarlierThan': '不早于',
    'condition.operator.notLaterThan': '不晚于',
    'condition.operator.in': '属于',
    'condition.operator.notIn': '不属于',
    'condition.fileType.image': '图片',
    'condition.fileType.document': '文档',
    'condition.fileType.video': '视频',
    'condition.fileType.audio': '音频',
    'condition.fileType.archive': '压缩包',
    'condition.fileType.code': '代码',
    'condition.fileType.data': '数据',
    'condition.fileType.3dModel': '3D模型',
    'condition.fileType.font': '字体',
    'condition.fileType.program': '程序',
    'condition.fileType.cad': 'CAD',
    'condition.fileType.ebook': '电子书',
    'condition.fileType.other': '其他',
    'condition.selectFileType': '选择文件类型',
    'condition.selectItemType': '选择项目类型',
    'condition.selectBooleanValue': '选择布尔值',
    'condition.enterValue': '输入数值',
    'condition.enterConditionValue': '输入条件值',
    'condition.itemType.file': '文件',
    'condition.itemType.folder': '文件夹',
    'condition.boolean.true': '是',
    'condition.boolean.false': '否',
    'condition.selectFolderEmptyState': '选择文件夹状态',
    'condition.folderState.empty': '为空',
    'condition.folderState.notEmpty': '不为空',
    'condition.selectProcessTargetFirst': '请先在基本设置中选择处理对象',
    // 文件筛选条件说明
    'condition.explanation.files.title': '文件筛选条件说明：',
    'condition.explanation.files.noCondition': '不添加任何条件 = 匹配所有文件',
    'condition.explanation.files.withCondition': '添加条件后只处理符合条件的文件',
    'condition.explanation.files.examples': '例如：文件大小>10MB、文件类型为图片、修改时间在7天内',
    'condition.explanation.files.combination': '可以组合多个条件进行精确筛选',

    // 文件夹筛选条件说明
    'condition.explanation.folders.title': '文件夹筛选条件说明：',
    'condition.explanation.folders.noCondition': '不添加任何条件 = 匹配所有文件夹',
    'condition.explanation.folders.withCondition': '添加条件后只处理符合条件的文件夹',
    'condition.explanation.folders.examples': '例如：文件夹为空、文件夹大小>100MB、文件夹深度>5层',
    'condition.explanation.folders.combination': '可以组合多个条件进行精确筛选',
    'condition.relationship': '条件关系:',
    'condition.relationship.and': '所有',
    'condition.relationship.or': '任一',
    'condition.conditionNumber': '条件 {number}',
    'condition.duplicateCondition': '复制条件',
    'condition.deleteCondition': '删除条件',
    'condition.conditionGroup': '条件组 ({count} 个条件)',
    'condition.addCondition': '添加条件',
    'condition.addConditionGroup': '添加条件组',

    // 日期类型选择
    'condition.dateType.absolute': '绝对',
    'condition.dateType.relative': '相对',

    // 相对日期单位
    'condition.relativeDateUnit.days': '天',
    'condition.relativeDateUnit.weeks': '周',
    'condition.relativeDateUnit.months': '月',
    'condition.relativeDateUnit.years': '年',

    // 相对日期方向
    'condition.relativeDateDirection.ago': '前',
    'condition.relativeDateDirection.within': '内',

    // 动作编辑器
    'action.type.move': '移动文件',
    'action.type.copy': '复制文件',
    'action.type.rename': '重命名文件',
    'action.type.delete': '删除文件',
    'action.type.moveFolder': '移动文件夹',
    'action.type.copyFolder': '复制文件夹',
    'action.type.renameFolder': '重命名文件夹',
    'action.type.deleteFolder': '删除文件夹',

    'action.type.createFolder': '创建文件夹',
    'action.type.moveDesc': '将文件移动到指定目录',
    'action.type.copyDesc': '将文件复制到指定目录',
    'action.type.renameDesc': '按照指定规则重命名文件',
    'action.type.deleteDesc': '删除文件（谨慎使用）',
    'action.type.moveFolderDesc': '将文件夹移动到指定目录',
    'action.type.copyFolderDesc': '将文件夹复制到指定目录',
    'action.type.renameFolderDesc': '按照指定规则重命名文件夹',
    'action.type.deleteFolderDesc': '删除文件夹（谨慎使用）',

    'action.type.createFolderDesc': '在指定位置创建新文件夹',
    'action.targetPath': '输出路径',
    'action.targetPathType': '输出路径类型',
    'action.targetPathType.inputFolder': '输入文件夹',
    'action.targetPathType.specificPath': '指定路径',
    'action.targetPathPlaceholder': '选择或输入输出路径',
    'action.browse': '浏览',
    'action.createSubfolders': '按文件类型自动分类',

    // 分类方式
    'action.classifyBy': '分类方式',
    'action.classify.none': '不分类',
    'action.classify.fileType': '按文件类型分类',
    'action.classify.createdDate': '按创建日期分类',
    'action.classify.modifiedDate': '按修改日期分类',
    'action.classify.fileSize': '按文件大小分类',
    'action.classify.extension': '按扩展名分类',
    'action.classify.preserveStructure': '保持文件夹结构',

    // 日期分组方式
    'action.classify.dateGrouping.year': '按年分组 (2024/)',
    'action.classify.dateGrouping.yearMonth': '按年月分组 (2024/01/)',
    'action.classify.dateGrouping.yearMonthDay': '按年月日分组 (2024/01/15/)',
    'action.classify.dateGrouping.quarter': '按季度分组 (2024/Q1/)',
    'action.classify.dateGrouping.monthName': '按月份名称分组 (2024/January/)',

    // 文件大小预设
    'action.classify.sizeMode': '大小分类模式',
    'action.classify.usePresetRanges': '使用预设范围',
    'action.classify.useCustomRanges': '自定义范围',
    'action.classify.presetScenario': '预设场景',
    'action.classify.sizePreset.general': '通用场景 (小/中/大/超大)',
    'action.classify.sizePreset.photo': '照片场景 (缩略图/普通/高清/RAW)',
    'action.classify.sizePreset.video': '视频场景 (短视频/标清/高清/4K)',

    // 自定义大小范围
    'action.classify.customSizeRanges': '自定义大小范围',
    'action.classify.useDefault': '使用默认',
    'action.classify.addRange': '添加范围',
    'action.classify.folderName': '文件夹名',
    'action.classify.minSize': '最小',
    'action.classify.maxSize': '最大',
    'action.classify.unit': '单位',
    'action.classify.unlimited': '无限制',
    'action.classify.newRange': '新范围',
    'action.classify.smallFiles': '小文件',
    'action.classify.mediumFiles': '中等文件',
    'action.classify.largeFiles': '大文件',
    'action.classify.extraLargeFiles': '超大文件',
    'action.classify.noCustomRanges': '暂无自定义范围，点击"使用默认"快速开始，或"添加范围"手动配置',
    'action.classify.customRangesTip': '💡 提示：最大值留空表示无限制，范围不能重叠',
    'action.classify.configHelp': '📋 配置说明：',
    'action.classify.configHelpItem1': '• 文件将根据大小自动分类到对应文件夹',
    'action.classify.configHelpItem2': '• 最大值留空表示无上限',
    'action.classify.configHelpItem3': '• 建议范围不要重叠，避免分类混乱',
    'action.classify.configHelpItem4': '• 支持B、KB、MB、GB四种单位',

    // 子文件夹处理配置
    'action.subfolderProcessing': '子文件夹处理',
    'action.includeSubfolders': '包含子文件夹',
    'action.maxDepth': '处理层级',
    'action.allLevels': '所有层级',
    'action.level1': '仅根目录',
    'action.level2': '仅第 1 层子目录',
    'action.level3': '仅第 2 层子目录',
    'action.level4': '仅第 3 层子目录',
    'action.level5': '仅第 4 层子目录',
    'action.allLevelsDesc': '将选择并处理所有层级的{{target}}。',
    'action.levelDesc': '将只选择并处理第 {{level}} 层子目录中的{{target}}。',
    'action.rootLevelDesc': '将只选择并处理根目录下的{{target}}。',
    'action.autoClassificationTitle': '自动分类规则：',
    'action.autoClassification.image': '图片：',
    'action.autoClassification.imageTypes': 'jpg, png, gif, webp...',
    'action.autoClassification.document': '文档：',
    'action.autoClassification.documentTypes': 'pdf, doc, txt, xlsx...',
    'action.autoClassification.video': '视频：',
    'action.autoClassification.videoTypes': 'mp4, avi, mkv, mov...',
    'action.autoClassification.audio': '音频：',
    'action.autoClassification.audioTypes': 'mp3, wav, flac, aac...',
    'action.autoClassification.archive': '压缩包：',
    'action.autoClassification.archiveTypes': 'zip, rar, 7z, tar...',
    'action.autoClassification.code': '代码：',
    'action.autoClassification.codeTypes': 'js, py, java, cpp...',
    'action.autoClassificationDesc': '系统会在目标路径下自动创建对应的分类文件夹',
    'action.pathPreview': '路径预览示例：',
    'action.pathPreview.imageFiles': '图片文件 → {path}/图片/',
    'action.pathPreview.documentFiles': '文档文件 → {path}/文档/',
    'action.pathPreview.videoFiles': '视频文件 → {path}/视频/',
    'action.pathPreview.otherFiles': '其他文件 → {path}/其他/',
    'action.pathPreview.allFiles': '所有文件 → {path}/',
    'action.namingRule': '命名规则',
    'action.naming.original': '保持原名',
    'action.naming.timestamp': '时间戳_原名',
    'action.naming.date': '日期_原名',
    'action.naming.fileCreated': '文件创建日期_原名',
    'action.naming.fileModified': '文件修改日期_原名',
    'action.naming.counter': '序号_原名',
    'action.naming.prefix': '添加前缀',
    'action.naming.suffix': '添加后缀',
    'action.naming.replace': '查找替换',
    'action.naming.case': '大小写转换',
    'action.naming.custom': '自定义模式',
    'action.naming.advanced': '高级组合模式',
    'action.dateFormat': '日期格式',
    'action.customPattern': '自定义模式',
    'action.customPatternPlaceholder': '例如: {date}_{name} 或 IMG_{counter}',
    'action.availableVariables': '可用变量：',
    'action.variable.name': '{name} - 原文件名（不含扩展名）',
    'action.variable.ext': '{ext} - 文件扩展名',
    'action.variable.date': '{date} - 当前日期 (YYYY-MM-DD)',
    'action.variable.time': '{time} - 当前时间 (HH-MM-SS)',
    'action.variable.counter': '{counter} - 自动递增序号',
    'action.variable.type': '{type} - 文件类型分类',
    'action.variable.year': '{year} - 年份 (2024)',
    'action.variable.month': '{month} - 月份 (01-12)',
    'action.variable.day': '{day} - 日期 (01-31)',
    'action.variable.hour': '{hour} - 小时 (00-23)',
    'action.variable.minute': '{minute} - 分钟 (00-59)',
    'action.variable.second': '{second} - 秒数 (00-59)',
    'action.examplePreview': '示例预览：',
    'action.previewError': '预览错误',
    'action.counterStart': '起始序号',
    'action.counterDigits': '序号位数',
    'action.prefixContent': '前缀内容',
    'action.prefixPlaceholder': '例如: 项目A_',
    'action.prefixExample': '示例: "项目A_" + "报告.docx" = "项目A_报告.docx"',
    'action.suffixContent': '后缀内容',
    'action.suffixPlaceholder': '例如: _备份',
    'action.suffixExample': '示例: "报告" + "_备份" + ".docx" = "报告_备份.docx"',
    'action.replaceFrom': '查找内容',
    'action.replaceFromPlaceholder': '要替换的文本',
    'action.replaceTo': '替换为',
    'action.replaceToPlaceholder': '新的文本（可为空）',
    'action.replaceExample': '示例: 查找"IMG" 替换为"照片" → "IMG_001.jpg" = "照片_001.jpg"',
    'action.caseType': '转换类型',
    'action.case.lower': '全部小写',
    'action.case.upper': '全部大写',
    'action.case.title': '首字母大写',
    'action.case.camel': '驼峰命名',
    'action.case.pascal': 'Pascal命名',
    'action.case.snake': '下划线命名',
    'action.case.kebab': '短横线命名',
    'action.removeSpaces': '移除空格',
    'action.removeSpecialChars': '移除特殊字符',
    'action.caseExample': '示例: "My File Name.txt" → "my_file_name.txt" (小写+下划线)',
    'action.advancedRules': '组合规则',
    'action.addRule': '添加规则',
    'action.ruleType': '类型',
    'action.ruleValue': '值',
    'action.ruleValuePlaceholder': '规则值',
    'action.advancedRuleTypes.prefix': '前缀',
    'action.advancedRuleTypes.suffix': '后缀',
    'action.advancedRuleTypes.replace': '替换',
    'action.advancedRuleTypes.case': '大小写',
    'action.advancedRuleTypes.counter': '序号',
    'action.advancedRuleTypes.date': '日期',
    'action.advancedRuleTypes.custom': '自定义',
    'action.advancedRulesEmpty': '点击"添加规则"开始创建组合重命名规则',
    'action.advancedRulesEmptyDesc': '规则将按顺序执行',
    'action.deleteRecycleTip': '删除后可在垃圾桶恢复',
    'action.deleteRecycleTipDesc': '文件将被移动到系统垃圾桶，可以随时恢复',
    'action.deleteEmptyFolders': '删除空文件夹',
    'action.deleteNonEmptyFolders': '删除非空文件夹（危险）',
    'action.preserveFolderStructure': '保持文件夹结构',
    'action.preserveFolderStructureDesc': '处理文件时保持原有的文件夹层级结构',
    'action.conflictWarning': '保持结构与文件类型分类不能同时启用',
    'action.mutuallyExclusiveNote': '此选项与"保持文件夹结构"/"按文件类型自动分类"互斥，勾选此项会自动取消另一项',
    'action.selectProcessTargetFirst': '请先在基本设置中选择处理对象',
    'action.actionType': '动作类型',
    'action.pathConfig': '路径配置',
    'action.namingConfig': '命名规则配置',
    'action.addAction': '添加处理动作',
    'action.noActions': '暂无处理动作',
    'action.noActionsDesc': '添加动作来定义文件的处理方式',
    // 文件处理动作配置指南
    'action.configGuide.files': '文件处理动作配置指南',
    'action.guide.files.classification': '自动分类整理：启用"按文件类型自动分类"，系统会智能创建分类文件夹',
    'action.guide.files.naming': '灵活命名规则：支持时间戳、序号、自定义模式等多种文件命名方式',
    'action.guide.files.filtering': '精确筛选：结合文件大小、类型、修改时间等条件进行精准处理',
    'action.guide.files.preview': '预览效果：配置完成后可在工作区预览实际处理效果',

    // 文件夹处理动作配置指南
    'action.configGuide.folders': '文件夹处理动作配置指南',
    'action.guide.folders.organization': '文件夹整理：移动、复制文件夹到指定位置进行分类管理',
    'action.guide.folders.cleanup': '清理空文件夹：自动检测并删除空文件夹，保持目录结构整洁',
    'action.guide.folders.structure': '结构优化：基于文件夹大小、深度、内容等条件进行结构调整',
    'action.guide.folders.preview': '预览效果：配置完成后可在工作区预览实际处理效果',
    'action.dateFormatLabel': '日期格式',
    'action.counterStartLabel': '起始序号',
    'action.counterDigitsLabel': '序号位数',
    'action.prefixContentLabel': '前缀内容',
    'action.suffixContentLabel': '后缀内容',
    'action.replaceFromLabel': '查找内容',
    'action.replaceToLabel': '替换为',
    'action.caseTypeLabel': '转换类型',
    'action.removeSpacesLabel': '移除空格',
    'action.removeSpecialCharsLabel': '移除特殊字符',
    'action.advancedRulesLabel': '组合规则',
    'action.addRuleButton': '添加规则',
    'action.replaceExampleText': '示例: 查找"IMG" 替换为"照片" → "IMG_001.jpg" = "照片_001.jpg"',
    'action.caseExampleText': '示例: "My File Name.txt" → "my_file_name.txt" (小写+下划线)',
    'action.filterRulesTitle': '筛选规则',
    'action.filterRulesDesc': '设置文件或文件夹的过滤条件',
    'action.filterInstructions': 'Filter Condition Instructions:',
    'action.noConditionsMatch': '无条件 = 匹配所有文件',
    'action.withConditionsMatch': '有条件时，只有满足条件的文件会被处理',
    'action.multipleConditions': '多个条件可以组合进行精确筛选',
    'action.availableVariablesTitle': '可用变量：',
    'action.examplePreviewTitle': '示例预览：',
    'action.originalFile': '原文件：',
    'action.newFile': '新文件：',
    'action.canAddMultipleActions': '可以添加多个动作，它们会按顺序执行',
    'action.onlyMatchingItemsProcessed': '只有符合条件的项目会被处理，如果不设置条件则处理所有项目',
    'action.customModeTitle': '自定义模式',
    'action.counterStartNumber': 'Counter起始数',
    'action.counterDigitsNumber': 'Counter位数',
    'action.counterExample': '示例: 起始数=101, 位数=3 → 101, 102, 103...',

    // 监控界面扩展
    'monitor.loading': '加载监控任务中...',
    'monitor.fileWatch': '文件监控',
    'monitor.scheduledTask': '定时任务',
    'monitor.associatedRule': '关联规则：',
    'monitor.executionCount': '执行次数：',
    'monitor.nextExecution': '下次执行：',
    'monitor.lastExecution': '上次执行：',
    'monitor.expired': '已过期',
    'monitor.aboutToExecute': '即将执行',
    'monitor.minutesLater': '{minutes}分钟后',
    'monitor.hoursLater': '{hours}小时后',
    'monitor.daysLater': '{days}天后',
    'monitor.unknownRule': '未知规则',
    'monitor.never': '从未执行',

    // 错误消息
    'error.loadMonitorDataFailed': '加载监控数据失败:',
    'error.loadTaskStatusFailed': '加载任务状态失败:',
    'error.toggleTaskStatusFailed': '切换任务状态失败:',
    'error.deleteTaskFailed': '删除任务失败:',
    'error.executeTaskFailed': '执行任务失败:',
    'error.createTaskFailed': '创建任务失败:',
    'error.sameSourceDestination': '源路径和目标路径不能相同',
    'error.updateTaskFailed': '更新任务失败:',
    'error.createRuleFailed': '创建规则失败:',
    'error.saveStepFailed': '保存步骤失败:',
    'error.addStepFailed': '添加步骤失败:',
    'error.toggleStepStatusFailed': '切换步骤状态失败:',
    'error.saveRuleFailed': '保存规则失败:',
    'error.saveRuleOrderFailed': '保存规则顺序失败:',
    'error.saveStepOrderFailed': '保存步骤顺序失败:',
    'error.deleteStepFailed': '删除步骤失败:',
    'error.resetFailed': '重置失败:',
    'error.selectPathFailed': '选择路径失败:',
    'error.selectFilesFailed': '选择文件失败:',
    'error.selectFolderFailed': '选择文件夹失败:',

    // 成功消息
    'success.taskExecutionCompleted': '任务执行完成:',
    'success.updateTaskData': '只更新相关任务的数据，避免整页刷新',
    'success.getUpdatedTaskData': '获取更新后的任务数据',
    'success.updateTaskStatus': '更新任务状态',
    'success.updateThisTaskOnly': '只更新这个任务的数据',
    'success.reloadData': '重新加载数据',
    'success.reloadDataSync': '重新加载数据以确保状态同步',
    'success.updateLocalState': '直接更新本地状态，避免重新加载整个列表',
    'success.updateLocalStateNoRefresh': '直接更新本地状态，不刷新整个列表',
    'success.closeFloatingEditor': '关闭悬浮编辑器',
    'success.updateOrderField': '更新order字段',
    'success.saveToBackend': '保存到后端',
    'success.updateRule': '更新规则，添加新步骤',
    'success.clearEditState': '清除编辑状态',
    'success.checkCompatibility': '检查当前操作符是否与新字段类型兼容',
    'success.updateFieldAndOperator': '同时更新字段和操作符（如果需要）',
    'success.avoidDuplicateAdd': '检查是否已存在相同ID的任务，避免重复添加',

    // 工作区界面
    'workspace.title': '工作区',
    'workspace.selectRule': '选择规则',
    'workspace.selectRulePlaceholder': '请选择要执行的规则',
    'workspace.noRules': '暂无可用规则',
    'workspace.noRulesDesc': '请先在规则中心创建并启用规则',
    'workspace.dragDropFiles': '拖拽文件到此处',
    'workspace.dragDropFilesDesc': '或点击选择文件',
    'workspace.selectFiles': '选择文件',
    'workspace.selectedFiles': '已选择 {count} 个文件',
    'workspace.selectedFolders': '已选择 {count} 个文件夹',
    'workspace.selectedFilesAndFolders': '已选择 {fileCount} 个文件和 {folderCount} 个文件夹',
    'workspace.items': '项',
    'workspace.folder': '文件夹',
    'workspace.folders': '文件夹',
    'workspace.files': '文件',
    'workspace.emptyFolder': '空文件夹',
    'workspace.noFilesSelected': '未选择文件',
    'workspace.preview': '预览',
    'workspace.execute': '执行',
    'workspace.cancel': '取消',
    'workspace.processing': '处理中...',
    'workspace.completed': '处理完成',
    'workspace.failed': '处理失败',
    'workspace.progress': '进度: {current}/{total}',
    'workspace.currentFile': '当前文件: {name}',
    'workspace.results': '处理结果',
    'workspace.previewResults': '预览结果',
    'workspace.executionResults': '执行结果',
    'workspace.successCount': '成功: {count} 个',
    'workspace.failedCount': '失败: {count} 个',
    'workspace.skippedCount': '跳过: {count} 个',
    'workspace.totalProcessed': '总计处理: {count} 个文件',
    'workspace.viewDetails': '查看详情',
    'workspace.hideDetails': '隐藏详情',
    'workspace.fileList': '文件列表',
    'workspace.fileName': '文件名',
    'workspace.filePath': '路径',
    'workspace.fileSize': '大小',
    'workspace.fileType': '类型',
    'workspace.status': '状态',
    'workspace.status.success': '成功',
    'workspace.status.failed': '失败',
    'workspace.status.skipped': '跳过',
    'workspace.status.pending': '等待',
    'workspace.newPath': '新路径',
    'workspace.error': '错误',
    'workspace.clearFiles': '清空文件',
    'workspace.refreshRules': '刷新规则',
    'workspace.loadingRules': '加载规则中...',
    'workspace.loadingFiles': '加载文件中...',
    'workspace.dragActive': '松开以添加文件',
    'workspace.supportedFormats': '支持所有文件格式',
    'workspace.maxFileSize': '单个文件最大 {size}',
    'workspace.confirmCancel': '确认取消',
    'workspace.confirmCancelDesc': '确定要取消当前处理吗？已处理的文件不会回滚。',
    'workspace.confirmClear': '确认清空',
    'workspace.confirmClearDesc': '确定要清空所有文件吗？',
    'workspace.oneClickMode': '一键执行模式',
    'workspace.oneClickModeDesc': '此规则将自动处理：{path}',
    'workspace.oneClickModeHint': '无需手动选择文件，点击执行即可开始整理',
    'workspace.dragDropArea': '拖拽文件或文件夹到这里',
    'workspace.pleaseSelectRule': '请先选择规则',
    'workspace.selectFilesButton': '选择文件',
    'workspace.selectFolderButton': '选择文件夹',
    'workspace.noExtension': '无扩展名',
    'workspace.noFiles': '暂无文件',
    'workspace.dragToStart': '拖拽文件到左侧区域开始',
    'workspace.fileCount': '{count} 个',
    'workspace.selectFilesTab': '文件选择',
    'workspace.fileListTab': '文件列表',
    'workspace.successLabel': '成功:',
    'workspace.durationLabel': '耗时:',
    'workspace.errorLabel': '错误:',
    'workspace.errorCount': '{count} 个',
    'workspace.executionStats': '执行统计',
    'workspace.originalPath': '原路径:',
    'workspace.originalName': '原文件名:',
    'workspace.totalFilesLabel': '总数:',
    'workspace.processedFilesLabel': '处理数:',
    'workspace.errorCountLabel': '错误数:',
    'workspace.stepCountLabel': '步骤数:',
    'workspace.newPathArrow': '→',
    'workspace.noChange': '无变化',
    'workspace.batchSelectSupport': '支持批量选择 • 自动识别文件类型',
    'workspace.noFilesTitle': '没有文件',
    'workspace.noFilesDesc': '请先选择要处理的文件或文件夹',
    'workspace.preparingProcess': '准备开始处理...',
    'workspace.executionCompleteTitle': '执行完成',
    'workspace.executionCompleteDesc': '规则执行完成！处理了 {count} 个文件',
    'workspace.processingFiles': '🔄 正在整理文件...',
    'workspace.errorPrefix': '错误: ',
    'workspace.totalFilesShort': '总数:',
    'workspace.processedShort': '处理:',
    'workspace.errorsShort': '错误:',
    'workspace.stepsShort': '步骤:',
    'workspace.currentLocation': '当前位置: ',
    'workspace.moreFilesCount': '还有 {count} 个文件...',
    'workspace.stepExecutionDetails': '步骤执行详情',
    'workspace.stepNumber': '步骤 {number}: {name}',
    'workspace.inputFiles': '输入: {count} 个文件',
    'workspace.outputFiles': '输出: {count} 个文件',
    'workspace.matchedFiles': '匹配: {count} 个文件',
    'workspace.input': '输入',
    'workspace.output': '输出',
    'workspace.processed': '处理',
    'workspace.errorDetails': '错误详情',
    'workspace.processingFile': '正在处理: {name}',
    'workspace.processingBatch': '正在处理批次 {current}/{total}',
    'workspace.cancelProcessing': '取消处理',
    'workspace.previewFailed': '预览失败，请检查规则配置',
    'workspace.loadDefaultFilesFailed': '加载默认路径文件失败:',
    'workspace.loadFailed': '加载失败',
    'workspace.loadFailedDesc': '无法加载路径 {path} 中的文件',
    'workspace.executionFailed': '执行失败',
    'workspace.executionFailedDesc': '执行失败，请检查规则配置和文件权限',
    'workspace.noPreview': '暂无预览',
    'workspace.clickPreview': '点击"预览结果"查看效果',
    'workspace.previewChanges': '预览变化',
    'workspace.fileLimitExceeded': '文件数量超出限制',
    'workspace.fileLimitExceededDesc': '当前已有 {{current}} 个文件，尝试添加 {{adding}} 个文件，总计 {{total}} 个，超出设置的上限 {{limit}} 个。请减少文件数量或在设置中调整上限。',
    'workspace.pathInfo': '路径信息',
    'workspace.rootPath': '根路径',
    'workspace.inputMismatchWarning': '输入文件可能不匹配',
    'workspace.detectedIssues': '检测到以下问题',
    'workspace.continuePreview': '是否继续预览？',
    'workspace.fileProcessingFailed': '文件处理失败',
    'workspace.fileProcessingFailedDesc': '处理文件时发生错误，请检查文件是否可访问或重试。',
    'workspace.noMatchingFiles': '无匹配文件',
    'workspace.workflowStepMismatch': '当前工作流步骤与输入文件不匹配',
    'workspace.noProcessingResult': '无处理结果',
    'workspace.workflowCompleteNoFiles': '工作流执行完成，但没有文件被处理',
    'workspace.selectedCount': '已选择 {count} 项',
    'workspace.selectedCountPending': '已选择 {count} 项待处理',
    'workspace.fileCountUnit': '{count} 个文件',
    'workspace.folderCountUnit': '{count} 个文件夹',
    'workspace.limitInfo': '上限 {limit}',
    'workspace.none': '无',

    // 文件类型不匹配相关翻译
    'workspace.fileTypeMismatch': '文件类型不匹配',
    'workspace.onlyFilesUploaded': '您上传了 {fileCount} 个文件',
    'workspace.onlyFoldersUploaded': '您上传了 {folderCount} 个文件夹',
    'workspace.workflowNeedsFolders': '但当前工作流只能处理文件夹。请选择文件夹或切换到支持文件的工作流。',
    'workspace.workflowNeedsFiles': '但当前工作流只能处理文件。请选择文件或切换到支持文件夹的工作流。',
    'workspace.switchWorkflowOrAddMatchingFiles': '请切换工作流或添加匹配的文件类型',
    'workspace.workflowCannotProcessInput': '当前工作流无法处理您选择的输入',
    'workspace.checkWorkflowTargetHint': '请检查工作流的处理目标设置或选择匹配的文件类型',
    'workspace.itemsUnit': '项',
    'workspace.moreChanges': '以下还有 {count} 个文件变化',
    'workspace.cannotProcessInputType': '工作流无法处理当前输入的文件类型',
    'workspace.stepNeedsFiles': '步骤"{stepName}"需要文件，但输入中只有文件夹',
    'workspace.stepNeedsFolders': '步骤"{stepName}"需要文件夹，但输入中只有文件',
    'workspace.stepSummary': '步骤{stepNumber}：{stepName} ({itemCount}个项目)',
    'workspace.withErrors': '，{errorCount} 个失败',
    'workspace.detailsLabel': '详细信息：',

    // 历史记录界面
    'history.title': '历史记录',
    'history.searchPlaceholder': '按文件名或规则搜索...',
    'history.refresh': '刷新',
    'history.clear': '清空',
    'history.loading': '加载历史记录中...',
    'history.noHistory': '暂无历史记录',
    'history.noHistoryDesc': '执行规则后，操作历史将显示在这里',
    'history.actions': '操作',
    'history.time': '时间',
    'history.workflow': '规则',
    'history.fileCount': '文件数',
    'history.result': '结果',
    'history.status': '状态',
    'history.workflowInfo': '规则: {name} • {source}',
    'history.source.manual': '手动执行',
    'history.source.fileWatch': '文件监控',
    'history.source.scheduled': '定时任务',
    'history.monitorTask': '监控任务: {name}',
    'history.processLog': '处理日志',
    'history.undoTime': '撤销于: {time}',
    'history.undone': '已撤销',
    'history.totalFiles': '总计: {count} 个文件',
    'history.processedFiles': '处理: {count} 个',
    'history.deleted': '已删除',
    'history.moreFiles': '还有 {count} 个文件...',
    'history.undo': '撤销',
    'history.redo': '重做',
    'history.failed': '失败',
    'history.partialSuccess': '部分成功',
    'history.completed': '已完成',
    'history.errors': '{count} 个错误',
    'history.statistics': '统计信息',
    'history.totalFileCount': '总文件数',
    'history.successCount': '成功处理',
    'history.failedCount': '处理失败',
    'history.duration':'执行时长',
    'history.fileOperations': '文件操作详情',
    'history.currentLocation': '当前位置:',
    'history.originalLocation': '原来位置:',
    'history.statusUndone': '已撤销到原位置',
    'history.originalPath': '原路径:',
    'history.newPath': '新路径:',
    'history.operationType': '操作类型:',
    'history.errorInfo': '错误信息:',
    'history.statusSuccess': '成功',
    'history.statusError': '失败',
    'history.statusProcessing': '处理中',
    'history.statusEmptyFolderCleaned': '空文件夹已清理',
    'history.statusEmptyFolderRestored': '已恢复空文件夹',
    'history.errorSummary': '错误汇总',
    'history.step': '步骤: {name}',
    'history.clearConfirmTitle': '清空历史记录',
    'history.clearConfirmDesc': '确定要清空所有历史记录吗？此操作不可撤销。',
    'history.deleteConfirmTitle': '删除历史记录',
    'history.deleteConfirmDesc': '确定要删除这条历史记录吗？',
    'history.undoSuccessTitle': '撤销成功',
    'history.undoSuccessDesc': '文件操作已成功撤销',
    'history.undoFailedTitle': '撤销失败',
    'history.undoFailedDesc': '撤销操作失败，可能是文件已被移动或删除',
    'history.undoFailedGeneric': '撤销操作失败，请检查文件状态',
    'history.redoSuccessTitle': '重做成功',
    'history.redoSuccessDesc': '文件操作已成功重做',
    'history.redoFailedTitle': '重做失败',
    'history.redoFailedDesc': '重做操作失败，可能是文件已被移动或删除',
    'history.redoFailedGeneric': '重做操作失败，请检查文件状态',
  },
  'en-US': {
    // 应用标题
    'app.title': 'ArroEngine',
    'app.subtitle': 'Smart File Organization Assistant',
    
    // 导航菜单
    'nav.workspace': 'Workspace',
    'nav.rules': 'Rule Center',
    'nav.monitoring': 'Monitoring',
    'nav.history': 'History',
    
    // 设置界面
    'settings.title': 'Settings',
    'settings.subtitle': 'Customize application settings',
    'settings.general': 'General',
    'settings.appearance': 'Appearance',
    'settings.workflowProcessing': 'Processing Management',
    'settings.history': 'History',
    'settings.storage': 'Storage',
    'settings.about': 'About',
    'settings.language': 'Language',
    'settings.selectLanguage': 'Select Language',
    'settings.selectLanguage.desc': 'Change the application display language',
    'settings.theme': 'Theme',
    'settings.theme.dark': 'Dark',
    'settings.theme.light': 'Light',
    'settings.uiScale': 'UI Scale',
    'settings.resetWindowSize': 'Reset to Default Size',
    'settings.resetWindowSize.desc': 'Reset window size to default proportions',
    'settings.resetWindowSize.success': 'Window size has been reset to default',
    'settings.resetWindowSize.failed': 'Failed to reset window size',
    'settings.minimizeToTray': 'Minimize to System Tray',
    'settings.minimizeToTray.desc': 'Minimize to system tray instead of exit when closing window',
    'settings.autoLaunch': 'Auto Launch',
    'settings.autoLaunch.desc': 'Start the application automatically when system boots',
    'settings.autoLaunchEnabled': 'Auto launch enabled',
    'settings.autoLaunchDisabled': 'Auto launch disabled',
    'settings.saved': 'Settings Saved',
    'settings.savedDesc': 'Your settings have been saved successfully',
    
    // 系统托盘
    'tray.hideWindow': 'Hide Window',
    'tray.showWindow': 'Show Window',
    'tray.enableAutoLaunch': 'Enable Auto Launch',
    'tray.disableAutoLaunch': 'Disable Auto Launch',
    'tray.exit': 'Exit',
    
    // 常规设置
    'settings.autoStart': 'Auto Start',
    'settings.autoStart.desc': 'Automatically run ArroEngine when system starts',
    
    // 历史记录设置
    'settings.maxHistoryEntries': 'Max History Entries',
    'settings.maxHistoryEntries.desc': 'Automatically delete oldest records when exceeding this number',
    'settings.autoCleanupDays': 'Auto Cleanup Days',
    'settings.autoCleanupDays.desc': 'Automatically delete records older than specified days',

    // Workflow Processing Settings
    'settings.workflowProcessing.maxItems': 'Processing Limit',
    'settings.workflowProcessing.maxItems.desc': 'Maximum number of files/folders that can be processed in a single operation',
    'settings.workflowProcessing.batchSize': 'Batch Size',
    'settings.workflowProcessing.batchSize.desc': 'Number of files processed per batch to avoid performance issues',
    'settings.workflowProcessing.batchInterval': 'Processing Interval',
    'settings.workflowProcessing.batchInterval.desc': 'Wait time between batches in milliseconds',

    'settings.clearAllHistory': 'Clear All History',
    'settings.clearAllHistory.desc': 'This operation cannot be undone, please proceed with caution',
    'settings.history.totalEntries': 'History Entries',
    'settings.history.totalFiles': 'Files Processed',
    'settings.history.activeDays': 'Active Days',
    'settings.history.confirmClear': 'Confirm Clear History',
    'settings.history.confirmClearDesc': 'This action will delete all history records and cannot be undone. Are you sure you want to continue?',
    'settings.history.confirmYes': 'Yes, clear history',
    'settings.history.confirmNo': 'Cancel',
    'settings.history.clearSuccess': 'History has been cleared',
    'settings.save': 'Save',

    // 存储管理
    'settings.appData': 'App Data',
    'settings.historyData': 'History Data',
    'settings.tempFiles': 'Temp Files',
    'settings.cleanTempFiles': 'Clean Temp Files',
    'settings.cleanTempFiles.desc': 'Clean temporary files and cache generated by the app',
    'settings.cleaning': 'Cleaning...',
    'settings.refreshData': 'Refresh Data',
    'settings.storageUsage': 'Storage Usage',
    
    // 关于
    'settings.about.subtitle': 'Intelligent File Organization Tool',
    'settings.about.version': 'Version',
    'settings.about.author': 'Author',
    'settings.about.authorName': 'ZKZ',
    'settings.about.features': 'Features',
    'settings.about.feature1': 'Intelligent file classification and organization',
    'settings.about.feature2': 'Real-time file monitoring',
    'settings.about.feature3': 'Scheduled task processing',
    'settings.about.feature4': 'Operation history and undo',
    'settings.about.feature5': 'Visual rule editing',
    'settings.about.checkUpdate': 'Check for Updates',
    'settings.about.checkUpdate.desc': 'Feature in development',
    'settings.about.updateNotice': 'Update checking feature is under development, stay tuned!',
    
    // 语言选项
    'language.zh-CN': '简体中文',
    'language.en-US': 'English',

    // 监控界面
    'monitor.title': 'Monitor Center',
    'monitor.description': 'Manage file monitoring and scheduled tasks',
    'monitor.createTask': 'Create Monitor Task',
    'monitor.noTasks': 'No monitor tasks',
    'monitor.createFirst': 'Create your first monitor task',
    'monitor.enable': 'Enable',
    'monitor.disable': 'Disable',
    'monitor.edit': 'Edit',
    'monitor.delete': 'Delete',
    'monitor.execute': 'Execute Now',
    'monitor.details': 'View Details',
    'monitor.status.running': 'Running',
    'monitor.status.stopped': 'Stopped',
    'monitor.status.error': 'Error',

    // 通用按钮和操作
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.create': 'Create',
    'common.import': 'Import',
    'common.export': 'Export',
    'common.confirm': 'Confirm',
    'common.dismiss': 'Dismiss',
    'common.validationError': 'Validation Error',
    'common.close': 'Close',
    'common.files': 'files',
    'common.folders': 'folders',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.finish': 'Finish',
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.warning': 'Warning',
    'common.info': 'Info',
    'common.yes': 'Yes',
    'common.no': 'No',

    // 监控任务相关
    'monitor.task.name': 'Task Name',
    'monitor.task.type': 'Task Type',
    'monitor.task.rule': 'Associated Rule',
    'monitor.task.status': 'Task Status',
    'monitor.task.lastExecution': 'Last Execution',
    'monitor.task.executionCount': 'Execution Count',
    'monitor.task.successRate': 'Success Rate',
    'monitor.task.fileWatch': 'File Watch',
    'monitor.task.scheduled': 'Scheduled',
    'monitor.task.enabled': 'Enabled',
    'monitor.task.disabled': 'Disabled',
    'monitor.task.running': 'Running',
    'monitor.task.stopped': 'Stopped',
    'monitor.task.error': 'Error',
    'monitor.task.never': 'Never executed',
    'monitor.task.idle': 'Idle',
    'monitor.task.waiting': 'Waiting',

    // 监控统计
    'monitor.stats.totalTasks': 'Total Tasks',
    'monitor.stats.runningTasks': 'Running',
    'monitor.stats.disabledTasks': 'Disabled',
    'monitor.stats.errorTasks': 'Error',

    // 监控任务创建/编辑
    'monitor.create.title': 'Create Monitor Task',
    'monitor.edit.title': 'Edit Monitor Task',
    'monitor.create.taskName': 'Task Name',
    'monitor.create.selectType': 'Select Task Type',
    'monitor.create.fileWatch.title': 'File Watch',
    'monitor.create.fileWatch.desc': 'Monitor file changes in specified folders',
    'monitor.create.scheduled.title': 'Scheduled Task',
    'monitor.create.scheduled.desc': 'Execute at specified time intervals',
    'monitor.create.selectRule': 'Select Rule',
    'monitor.create.selectRule.placeholder': 'Please select a rule to execute',
    'monitor.create.watchPaths': 'Watch Paths',
    'monitor.create.addPath': 'Add Path',
    'monitor.create.removePath': 'Remove Path',
    'monitor.create.browsePath': 'Browse',
    'monitor.create.enableAutoExecution': 'Enable Auto Execution',
    'monitor.create.batchSize': 'Batch Size',
    'monitor.create.batchSize.desc': 'Number of files to process per batch',
    'monitor.create.debounceDelay': 'Processing Interval',
    'monitor.create.debounceDelay.desc': 'Wait time after file changes (milliseconds)',
    'monitor.create.schedule': 'Schedule',
    'monitor.create.schedule.placeholder': 'Enter cron expression',
    'monitor.create.schedule.examples': 'Example: 0 */1 * * * (Execute every hour)',

    // 监控任务创建/编辑 - 新增翻译键
    'monitor.create.fillRequired': 'Please fill in task name and select rule',
    'monitor.create.fillWatchPath': 'Please fill in watch path',
    'monitor.create.fillInputPath': 'Please fill in input path',
    'monitor.create.fillCronExpression': 'Please fill in cron expression',
    'monitor.create.basicInfo': 'Basic Information',
    'monitor.create.taskNamePlaceholder': 'Enter task name',
    'monitor.create.createTask': 'Create Task',
    'monitor.create.taskDescription': 'Task Description',
    'monitor.create.taskDescriptionPlaceholder': 'Enter task description (optional)',
    'monitor.create.taskType': 'Task Type',
    'monitor.create.fileWatch': 'File Watch',
    'monitor.create.scheduled': 'Scheduled Task',
    'monitor.create.associatedRule': 'Associated Rule',
    'monitor.create.fileWatchConfig': 'File Watch Configuration',
    'monitor.create.watchPath': 'Watch Path',
    'monitor.create.watchPathPlaceholder': 'Select folder to monitor',
    'monitor.create.addWatchPath': 'Add Watch Path',
    'monitor.create.includeSubfolders': 'Include Subfolders',
    'monitor.create.ignorePatterns': 'Ignore Patterns',
    'monitor.create.ignorePatternPlaceholder': 'e.g.: *.tmp or node_modules',
    'monitor.create.ignorePatternsDesc': 'Set files or folders to ignore',
    'monitor.create.addIgnorePattern': '+ Add Ignore Pattern',
    'monitor.create.availablePatternsTitle': 'Practical Ignore Patterns:',
    'monitor.create.patternExamplesTitle': 'Usage Scenarios:',
    'monitor.create.pattern.specificFile': 'Important Document.docx - Ignore specific file',
    'monitor.create.pattern.specificFolder': 'Important Materials - Ignore specific folder',
    'monitor.create.pattern.fileType': '*.psd - Ignore all PSD design files',
    'monitor.create.pattern.multipleTypes': '*.{exe,msi} - Ignore all installer files',
    'monitor.create.pattern.folderContents': 'Private Files/* - Ignore all contents in folder',
    'monitor.create.pattern.namePattern': '*Backup* - Ignore files with "Backup" in name',
    'monitor.create.pattern.systemFiles': 'desktop.ini - Ignore system config files',
    'monitor.create.pattern.hiddenFiles': '.* - Ignore all hidden files',
    'monitor.create.pattern.tempFiles': '*~* - Ignore temporary edit files',
    'monitor.create.pattern.cacheFiles': 'Thumbs.db - Ignore thumbnail cache',
    'monitor.create.scenarioTitle': 'Usage Scenarios:',
    'monitor.create.scenario.specificFile': 'Ignore specific file:',
    'monitor.create.scenario.specificFolder': 'Ignore specific folder:',
    'monitor.create.scenario.fileTypes': 'Ignore certain file types:',
    'monitor.create.scenario.nameContains': 'Ignore files with specific words in name:',
    'monitor.create.wildcardRulesTitle': 'Wildcard Rules:',
    'monitor.create.wildcard.star': '* - Match any characters',
    'monitor.create.wildcard.question': '? - Match single character',
    'monitor.create.wildcard.brackets': '\\{jpg,png\\} - Match multiple extensions',
    'monitor.create.wildcard.range': '[0-9] - Match number range',
    'monitor.create.wildcard.negation': '[!abc] - Do not match specified characters',
    'monitor.create.watchEvents': 'Watch Events',
    'monitor.create.watchEventsDesc': 'Select file events that trigger rule execution. When monitored events occur, the associated rules will be executed automatically',
    'monitor.create.advancedSettings': 'Advanced Settings',
    'monitor.create.skipIfRunning': 'Skip If Running',
    'monitor.create.skipIfRunningDesc': 'Skip this execution if the previous task is still running to avoid task overlap',
    'monitor.create.eventAdd': 'File Created',
    'monitor.create.eventChange': 'File Modified',
    'monitor.create.eventDelete': 'File Deleted',
    'monitor.create.eventAddDir': 'Folder Created',
    'monitor.create.eventDeleteDir': 'Folder Deleted',
    'monitor.create.commonPatterns': 'Common Patterns',
    'monitor.create.ignoreTempFiles': 'Temp Files',
    'monitor.create.ignoreLogFiles': 'Log Files',
    'monitor.create.ignoreBackupFiles': 'Backup Files',
    'monitor.create.ignoreCacheFolder': 'Cache Folder',
    'monitor.create.ignoreTempFolder': 'Temp Folder',
    'monitor.create.ignoreNodeModules': 'Node Modules',
    'monitor.create.example.importantDoc': 'Important Document.docx',
    'monitor.create.example.importantFiles': 'Important Materials',
    'monitor.create.example.privateFiles': 'Private Files',
    'monitor.create.example.backup': '*Backup*',
    'monitor.create.scheduledConfig': 'Scheduled Task Configuration',
    'monitor.create.inputPath': 'Input Path',
    'monitor.create.inputPathPlaceholder': 'Select folder to process',
    'monitor.create.scheduleTime': 'Schedule Time',
    'monitor.create.daily': 'Daily',
    'monitor.create.hourly': 'Hourly',
    'monitor.create.weekly': 'Weekly',
    'monitor.create.custom': 'Custom',
    'monitor.create.executeTime': 'Execute Time',
    'monitor.create.recursiveProcess': 'Recursive Process',

    // 监控任务详情
    'monitor.detail.basicInfo': 'Basic Information',
    'monitor.detail.expired': 'Expired',
    'monitor.detail.aboutToRun': 'About to run',
    'monitor.detail.minutesLater': '{minutes} minutes later',
    'monitor.detail.hoursLater': '{hours} hours later',
    'monitor.detail.daysLater': '{days} days later',
    'monitor.detail.disabled': 'Disabled',
    'monitor.detail.running': 'Running',
    'monitor.detail.error': 'Error',
    'monitor.detail.waiting': 'Waiting',
    'monitor.detail.idle': 'Idle',
    'monitor.detail.fileWatch': 'File Watch',
    'monitor.detail.scheduled': 'Scheduled Task',
    'monitor.detail.executeNow': 'Execute Now',
    'monitor.detail.disableTask': 'Disable Task',
    'monitor.detail.enableTask': 'Enable Task',
    'monitor.detail.editTask': 'Edit Task',
    'monitor.detail.taskDescription': 'Task Description:',
    'monitor.detail.noDescription': 'No description',
    'monitor.detail.associatedRule': 'Associated Rule:',
    'monitor.detail.unknownRule': 'Unknown rule',
    'monitor.detail.createdAt': 'Created At:',
    'monitor.detail.updatedAt': 'Updated At:',
    'monitor.detail.lastExecuted': 'Last Executed:',
    'monitor.detail.nextExecution': 'Next Execution:',
    'monitor.detail.executionStats': 'Execution Statistics',
    'monitor.detail.totalExecutions': 'Total Executions',
    'monitor.detail.successfulExecutions': 'Successful',
    'monitor.detail.failedExecutions': 'Failed',
    'monitor.detail.processedFiles': 'Processed Files',
    'monitor.detail.averageExecutionTime': 'Average Execution Time:',
    'monitor.detail.lastExecutionDuration': 'Last Execution Duration:',
    'monitor.detail.recentError': 'Recent Error',
    'monitor.detail.fileWatchConfig': 'File Watch Configuration',
    'monitor.detail.scheduledConfig': 'Scheduled Task Configuration',
    'monitor.detail.watchPaths': 'Watch Paths:',
    'monitor.detail.watchEvents': 'Watch Events:',
    'monitor.detail.includeSubfolders': 'Include Subfolders:',
    'monitor.detail.debounceDelay': 'Processing Interval:',
    'monitor.detail.batchSize': 'Batch Size:',
    'monitor.detail.unlimited': 'Unlimited',
    'monitor.detail.inputPath': 'Input Path:',
    'monitor.detail.cronExpression': 'Cron Expression:',
    'monitor.detail.timezone': 'Timezone:',
    'monitor.detail.skipIfRunning': 'Skip If Running:',
    'monitor.detail.totalFileCount': 'Total Files',
    'monitor.detail.successCount': 'Successful',
    'monitor.detail.failedCount': 'Failed',
    'monitor.detail.duration': 'Duration',
    'monitor.detail.fileOperations': 'File Operations',
    'monitor.detail.currentLocation': 'Current Location:',
    'monitor.detail.originalLocation': 'Original Location:',
    'monitor.detail.status': 'Status',
    'monitor.detail.statusUndone': 'Undone to original location',
    'monitor.detail.originalPath': 'Original Path:',
    'monitor.detail.newPath': 'New Path:',
    'monitor.detail.operationType': 'Operation Type:',
    'monitor.detail.errorInfo': 'Error Info:',
    'monitor.detail.statusSuccess': 'Success',
    'monitor.detail.statusError': 'Failed',
    'monitor.detail.statusProcessing': 'Processing',
    'monitor.detail.errorSummary': 'Error Summary',
    'monitor.detail.step': 'Step: {name}',
    'monitor.detail.processLog': 'Process Log',

    // 监控任务操作确认
    'monitor.deleteConfirmTitle': 'Delete Monitor Task',
    'monitor.deleteConfirmDesc': 'Are you sure you want to delete monitor task "{name}"? This action cannot be undone.',
    'monitor.executeFailedTitle': 'Task Execution Issue',
    'monitor.executeFailedDesc': 'An issue occurred while executing the task. Please check the following information:\n\n{error}',
    'monitor.createFailedTitle': 'Task Creation Failed',
    'monitor.createFailedDesc': 'An issue occurred while creating the monitor task:\n\n{error}',
    'monitor.updateFailedTitle': 'Task Update Failed',
    'monitor.updateFailedDesc': 'An issue occurred while updating the monitor task:\n\n{error}',

    // 历史记录详细
    'history.details.title': 'History Details',
    'history.details.executionInfo': 'Execution Information',
    'history.details.fileOperations': 'File Operations',
    'history.details.errors': 'Error Information',
    'history.details.duration': 'Duration',
    'history.details.startTime': 'Start Time',
    'history.details.endTime': 'End Time',
    'history.details.totalFiles': 'Total Files',
    'history.details.processedFiles': 'Processed Files',
    'history.details.operation.move': 'Move',
    'history.details.operation.copy': 'Copy',
    'history.details.operation.rename': 'Rename',
    'history.details.operation.delete': 'Delete',
    'history.details.operation.cleanup_empty_folder': 'Cleanup Empty Folder',
    'history.details.originalPath': 'Original Path',
    'history.details.newPath': 'New Path',
    'history.details.noOperations': 'No file operations',
    'history.details.noErrors': 'No errors',

    // 确认对话框
    'confirm.delete.title': 'Confirm Delete',
    'confirm.delete.message': 'Are you sure you want to delete this item? This action cannot be undone.',
    'confirm.clearHistory.title': 'Confirm Clear',
    'confirm.clearHistory.message': 'Are you sure you want to clear all history records? This action cannot be undone.',
    'confirm.undo.title': 'Confirm Undo',
    'confirm.undo.message': 'Are you sure you want to undo this operation?',
    'confirm.default.confirm': 'Confirm',
    'confirm.default.cancel': 'Cancel',

    // 规则中心界面
    'rule.center.title': 'Rule Center',
    'rule.center.resetDefault': 'Reset Default',
    'rule.center.createRule': 'Create Rule',
    'rule.center.loading': 'Loading...',
    'rule.center.noRules': 'No rules',
    'rule.center.noRulesDesc': 'Click "Reset Default" to load preset rules',
    'rule.center.selectRule': 'Select Rule',
    'rule.center.selectRuleDesc': 'Select a rule from the left to view details',
    'rule.center.newRule': 'New Rule',
    'rule.center.newRuleDesc': 'Please edit rule description',
    'rule.center.createFailed': 'Creation Failed',
    'rule.center.createFailedDesc': 'Failed to create rule, please try again',
    'rule.center.saveFailed': 'Save Failed',
    'rule.center.saveFailedDesc': 'Save failed, please try again',
    'rule.center.saveSuccess': 'Save Successful',
    'rule.center.saveSuccessDesc': 'Step saved successfully!',
    'rule.center.resetTitle': 'Reset Default Rules',
    'rule.center.resetDesc': 'Are you sure you want to reset default rules? This will restore or add missing default rules without affecting your custom rules.',
    'rule.center.resetConfirm': 'Reset',
    'rule.center.deleteRule': 'Delete Rule',
    'rule.center.deleteRuleDesc': 'Are you sure you want to delete rule "{name}"? This will delete the rule and all its steps and cannot be undone.',
    'rule.center.deleteStep': 'Delete Step',
    'rule.center.deleteStepDesc': 'Are you sure you want to delete step "{name}"? This action cannot be undone.',
    'rule.center.stepsCount': '{count} steps',
    'rule.center.enabled': 'Enabled',
    'rule.center.disabled': 'Disabled',
    'rule.center.enable': 'Enable',
    'rule.center.disable': 'Disable',
    'rule.center.edit': 'Edit',
    'rule.center.delete': 'Delete',
    'rule.center.addStep': 'Add Step',
    'rule.center.processSteps': 'Process Steps',
    'rule.center.autoCleanupEnabled': 'Cleanup empty folders after processing',
    'rule.center.autoCleanupDisabled': 'Keep empty folders after processing',
    'rule.center.includeSubfoldersEnabled': 'Include subfolders',
    'rule.center.includeSubfoldersDisabled': 'Don\'t include subfolders',
    'rule.center.goToWorkspace': 'Go to Workspace Test',
    'rule.center.doubleClickEditName': 'Double click to edit name',
    'rule.center.doubleClickEditDesc': 'Double click to edit description',
    'rule.center.nameMaxLength': 'Max {max} characters',
    'rule.center.descMaxLength': 'Max {max} characters',
    'rule.center.deleteRuleTooltip': 'Delete rule',
    'rule.center.dragHandle': 'Drag handle',
    'rule.center.enableDisableButton': 'Enable/Disable button',
    'rule.center.editButton': 'Edit button',
    'rule.center.deleteButton': 'Delete button',
    'rule.center.filterConditions': 'Filter conditions: {count}',
    'rule.center.processActions': 'Process actions: {count}',
    'rule.center.processTarget': 'Process Target',
    'rule.center.processTarget.files': 'Files',
    'rule.center.processTarget.folders': 'Folders',
    'rule.center.inputSource': 'Input Source',
    'rule.center.inputSource.original': 'Initial Directory',
    'rule.center.inputSource.previousStep': 'Previous Step Output',
    'rule.center.inputSource.specificPath': 'Specific Path',
    'rule.center.conditions': 'Conditions',
    'rule.center.actions': 'Actions',
    'rule.center.actionDetails': 'Action Details',
    'rule.center.conditionCount': 'conditions',
    'rule.center.actionCount': 'actions',
    'rule.center.noConditions': 'No conditions',
    'rule.center.noActions': 'No actions configured',
    'rule.center.notConfigured': 'Not configured',
    'rule.center.moreActions': 'more actions',
    'rule.center.moreConditions': 'more conditions',
    'rule.center.condition': 'Condition',
    'rule.center.action': 'Action',
    'rule.center.andMore': '{{count}} more',
    'rule.center.actionType.move': 'Move',
    'rule.center.actionType.copy': 'Copy',
    'rule.center.actionType.rename': 'Rename',
    'rule.center.actionType.delete': 'Delete',
    'rule.center.stepName': 'Step {number}',
    'rule.center.stepDesc': 'Describe the function of this step',
    'rule.center.unknownRule': 'Unknown rule',
    'rule.center.unknownStep': 'Unknown step',

    // 悬浮步骤编辑器
    'stepEditor.title': 'Edit Step: {name}',
    'stepEditor.rule': 'Rule: {name}',
    'stepEditor.save': 'Save',
    'stepEditor.cancel': 'Cancel',
    'stepEditor.stepName': 'Step Name',
    'stepEditor.stepNamePlaceholder': 'Enter step name',
    'stepEditor.stepDesc': 'Step Description',
    'stepEditor.stepDescPlaceholder': 'Describe step function',
    'stepEditor.inputSource': 'Input Source',
    'stepEditor.inputSource.original': 'Initial Directory',
    'stepEditor.inputSource.previousStep': 'Specific Step Output',
    'stepEditor.inputSource.specificPath': 'Specific Path',
    'stepEditor.targetPath': 'Input Path',
    'stepEditor.targetPathPlaceholder': 'Select or enter input path',
    'stepEditor.specificPathDesc': 'This step will reload files from the specified path instead of using output from previous steps',
    'stepEditor.originalDesc': 'Use current workflow state files (skip disabled steps)',
    'stepEditor.previousStepDesc': 'Use output from specific step (supports branch processing)',
    'stepEditor.selectStep': 'Select Step',
    'stepEditor.selectStepPlaceholder': 'Select step to use output from',
    'stepEditor.lastStep': 'Last Step',
    'stepEditor.step': 'Step',
    'stepEditor.specificPathRequired': 'Path must be specified when using specific path input source',
    'stepEditor.browse': 'Browse',
    'stepEditor.filterConditions': 'Filter Conditions',
    'stepEditor.filterConditionsDesc': 'Set file filtering rules, only files meeting conditions will be processed',
    'stepEditor.processActions': 'Process Actions',
    'stepEditor.processActionsDesc': 'Define operations to perform on files meeting conditions, executed in order',
    'stepEditor.basicSettings': 'Basic Settings',
    'stepEditor.basicSettingsDesc': 'Configure basic information and processing targets for the step',
    'stepEditor.processTarget': 'Process Target',
    'stepEditor.processTarget.files': 'Files',
    'stepEditor.processTarget.filesDesc': 'Process files in folders',
    'stepEditor.processTarget.folders': 'Folders',
    'stepEditor.processTarget.foldersDesc': 'Process folders themselves',

    'stepEditor.required': 'Required',
    'stepEditor.processTargetRequired': 'Please select the target object type to process, which will determine the available filter conditions and process actions.',
    'stepEditor.inputSourceTitle': 'Input Source',
    'stepEditor.tabBasic': 'Basic',
    'stepEditor.tabConditions': 'Filter',
    'stepEditor.tabActions': 'Actions',

    // Step editor validation
    'stepEditor.validation.targetPathRequired': 'Please set output path for move, copy or create folder actions',
    'stepEditor.validation.processTargetRequired': 'Please select process target (files or folders)',
    'stepEditor.validation.actionsRequired': 'Please add at least one process action',

    // 条件编辑器
    'condition.field.fileName': 'File Name',
    'condition.field.fileExtension': 'Extension',
    'condition.field.fileSize': 'File Size',
    'condition.field.fileType': 'File Type',
    'condition.field.createdDate': 'Created',
    'condition.field.modifiedDate': 'Modified',
    'condition.field.filePath': 'File Path',
    'condition.field.folderName': 'Folder Name',
    'condition.field.folderSize': 'Folder Size',
    'condition.field.folderFileCount': 'Files',
    'condition.field.folderSubfolderCount': 'Subfolders',
    'condition.field.folderIsEmpty': 'Is Empty',
    'condition.field.itemType': 'Item Type',
    'condition.operator.contains': 'Contains',
    'condition.operator.notContains': 'Not Contains',
    'condition.operator.equals': 'Equals',
    'condition.operator.notEquals': 'Not Equals',
    'condition.operator.is': 'Is',
    'condition.operator.startsWith': 'Starts With',
    'condition.operator.notStartsWith': 'Not Starts',
    'condition.operator.endsWith': 'Ends With',
    'condition.operator.notEndsWith': 'Not Ends',
    'condition.operator.regex': 'Regex Match',
    'condition.operator.greaterThan': 'Greater Than',
    'condition.operator.lessThan': 'Less Than',
    'condition.operator.greaterThanOrEqual': 'Greater Than or Equal',
    'condition.operator.lessThanOrEqual': 'Less Than or Equal',
    'condition.operator.laterThan': 'After',
    'condition.operator.earlierThan': 'Before',
    'condition.operator.notEarlierThan': 'Not Before',
    'condition.operator.notLaterThan': 'Not After',
    'condition.operator.in': 'In',
    'condition.operator.notIn': 'Not In',
    'condition.fileType.image': 'Image',
    'condition.fileType.document': 'Document',
    'condition.fileType.video': 'Video',
    'condition.fileType.audio': 'Audio',
    'condition.fileType.archive': 'Archive',
    'condition.fileType.code': 'Code',
    'condition.fileType.data': 'Data',
    'condition.fileType.3dModel': '3D Model',
    'condition.fileType.font': 'Font',
    'condition.fileType.program': 'Program',
    'condition.fileType.cad': 'CAD',
    'condition.fileType.ebook': 'E-book',
    'condition.fileType.other': 'Other',
    'condition.selectFileType': 'Select file type',
    'condition.selectItemType': 'Select item type',
    'condition.selectBooleanValue': 'Select boolean value',
    'condition.enterValue': 'Enter value',
    'condition.enterConditionValue': 'Enter condition value',
    'condition.itemType.file': 'File',
    'condition.itemType.folder': 'Folder',
    'condition.boolean.true': 'Yes',
    'condition.boolean.false': 'No',
    'condition.selectFolderEmptyState': 'Select folder state',
    'condition.folderState.empty': 'Empty',
    'condition.folderState.notEmpty': 'Not Empty',
    'condition.selectProcessTargetFirst': 'Please select process target in basic settings first',
    // File filter condition instructions
    'condition.explanation.files.title': 'File Filter Condition Instructions:',
    'condition.explanation.files.noCondition': 'No conditions = match all files',
    'condition.explanation.files.withCondition': 'With conditions, only files meeting criteria will be processed',
    'condition.explanation.files.examples': 'Examples: file size > 10MB, image files, modified within 7 days',
    'condition.explanation.files.combination': 'Multiple conditions can be combined for precise filtering',

    // Folder filter condition instructions
    'condition.explanation.folders.title': 'Folder Filter Condition Instructions:',
    'condition.explanation.folders.noCondition': 'No conditions = match all folders',
    'condition.explanation.folders.withCondition': 'With conditions, only folders meeting criteria will be processed',
    'condition.explanation.folders.examples': 'Examples: empty folders, folder size > 100MB, folder depth > 5 levels',
    'condition.explanation.folders.combination': 'Multiple conditions can be combined for precise filtering',
    'condition.relationship': 'Condition relationship:',
    'condition.relationship.and': 'AND',
    'condition.relationship.or': 'OR',
    'condition.conditionNumber': 'Condition {number}',
    'condition.duplicateCondition': 'Duplicate condition',
    'condition.deleteCondition': 'Delete condition',
    'condition.conditionGroup': 'Condition group ({count} conditions)',
    'condition.addCondition': 'Add Condition',
    'condition.addConditionGroup': 'Add Condition Group',

    // Date type selection
    'condition.dateType.absolute': 'Absolute',
    'condition.dateType.relative': 'Relative',

    // Relative date units
    'condition.relativeDateUnit.days': 'Days',
    'condition.relativeDateUnit.weeks': 'Weeks',
    'condition.relativeDateUnit.months': 'Months',
    'condition.relativeDateUnit.years': 'Years',

    // Relative date directions
    'condition.relativeDateDirection.ago': 'Ago',
    'condition.relativeDateDirection.within': 'Within',

    // 动作编辑器
    'action.type.move': 'Move File',
    'action.type.copy': 'Copy File',
    'action.type.rename': 'Rename File',
    'action.type.delete': 'Delete File',
    'action.type.moveFolder': 'Move Folder',
    'action.type.copyFolder': 'Copy Folder',
    'action.type.renameFolder': 'Rename Folder',
    'action.type.deleteFolder': 'Delete Folder',

    'action.type.createFolder': 'Create Folder',
    'action.type.moveDesc': 'Move files to specified directory',
    'action.type.copyDesc': 'Copy files to specified directory',
    'action.type.renameDesc': 'Rename files according to specified rules',
    'action.type.deleteDesc': 'Delete files (use with caution)',
    'action.type.moveFolderDesc': 'Move folders to specified directory',
    'action.type.copyFolderDesc': 'Copy folders to specified directory',
    'action.type.renameFolderDesc': 'Rename folders according to specified rules',
    'action.type.deleteFolderDesc': 'Delete folders (use with caution)',

    'action.type.createFolderDesc': 'Create new folder at specified location',
    'action.targetPath': 'Output Path',
    'action.targetPathType': 'Output Path Type',
    'action.targetPathType.inputFolder': 'Input Folder',
    'action.targetPathType.specificPath': 'Specific Path',
    'action.targetPathPlaceholder': 'Select or enter output path',
    'action.browse': 'Browse',
    'action.createSubfolders': 'Auto-classify by file type',

    // Classification methods
    'action.classifyBy': 'Classification Method',
    'action.classify.none': 'No Classification',
    'action.classify.fileType': 'Classify by File Type',
    'action.classify.createdDate': 'Classify by Created Date',
    'action.classify.modifiedDate': 'Classify by Modified Date',
    'action.classify.fileSize': 'Classify by File Size',
    'action.classify.extension': 'Classify by Extension',
    'action.classify.preserveStructure': 'Preserve Folder Structure',

    // Date grouping methods
    'action.classify.dateGrouping.year': 'Group by Year (2024/)',
    'action.classify.dateGrouping.yearMonth': 'Group by Year-Month (2024/01/)',
    'action.classify.dateGrouping.yearMonthDay': 'Group by Year-Month-Day (2024/01/15/)',
    'action.classify.dateGrouping.quarter': 'Group by Quarter (2024/Q1/)',
    'action.classify.dateGrouping.monthName': 'Group by Month Name (2024/January/)',

    // File size presets
    'action.classify.sizeMode': 'Size Classification Mode',
    'action.classify.usePresetRanges': 'Use Preset Ranges',
    'action.classify.useCustomRanges': 'Custom Ranges',
    'action.classify.presetScenario': 'Preset Scenario',
    'action.classify.sizePreset.general': 'General (Small/Medium/Large/XLarge)',
    'action.classify.sizePreset.photo': 'Photo (Thumbnail/Normal/HD/RAW)',
    'action.classify.sizePreset.video': 'Video (Short/SD/HD/4K)',

    // Custom size ranges
    'action.classify.customSizeRanges': 'Custom Size Ranges',
    'action.classify.useDefault': 'Use Default',
    'action.classify.addRange': 'Add Range',
    'action.classify.folderName': 'Folder Name',
    'action.classify.minSize': 'Min',
    'action.classify.maxSize': 'Max',
    'action.classify.unit': 'Unit',
    'action.classify.unlimited': 'Unlimited',
    'action.classify.newRange': 'New Range',
    'action.classify.smallFiles': 'Small Files',
    'action.classify.mediumFiles': 'Medium Files',
    'action.classify.largeFiles': 'Large Files',
    'action.classify.extraLargeFiles': 'Extra Large Files',
    'action.classify.noCustomRanges': 'No custom ranges yet. Click "Use Default" to get started quickly, or "Add Range" to configure manually',
    'action.classify.customRangesTip': '💡 Tip: Leave max value empty for unlimited, avoid overlapping ranges',
    'action.classify.configHelp': '📋 Configuration Guide:',
    'action.classify.configHelpItem1': '• Files will be automatically classified into corresponding folders by size',
    'action.classify.configHelpItem2': '• Empty max value means unlimited',
    'action.classify.configHelpItem3': '• Avoid overlapping ranges to prevent classification confusion',
    'action.classify.configHelpItem4': '• Supports B, KB, MB, GB units',

    // Subfolder processing configuration
    'action.subfolderProcessing': 'Subfolder Processing',
    'action.includeSubfolders': 'Include Subfolders',
    'action.maxDepth': 'Processing Depth',
    'action.allLevels': 'All Levels',
    'action.level1': 'Root Directory Only',
    'action.level2': 'Level 1 Subdirectories Only',
    'action.level3': 'Level 2 Subdirectories Only',
    'action.level4': 'Level 3 Subdirectories Only',
    'action.level5': 'Level 4 Subdirectories Only',
    'action.allLevelsDesc': 'Will select and process {{target}} at all levels.',
    'action.levelDesc': 'Will only select and process {{target}} in level {{level}} subdirectories.',
    'action.rootLevelDesc': 'Will only select and process {{target}} in the root directory.',
    'action.autoClassificationTitle': 'Auto-classification rules:',
    'action.autoClassification.image': 'Images:',
    'action.autoClassification.imageTypes': 'jpg, png, gif, webp...',
    'action.autoClassification.document': 'Documents:',
    'action.autoClassification.documentTypes': 'pdf, doc, txt, xlsx...',
    'action.autoClassification.video': 'Videos:',
    'action.autoClassification.videoTypes': 'mp4, avi, mkv, mov...',
    'action.autoClassification.audio': 'Audio:',
    'action.autoClassification.audioTypes': 'mp3, wav, flac, aac...',
    'action.autoClassification.archive': 'Archives:',
    'action.autoClassification.archiveTypes': 'zip, rar, 7z, tar...',
    'action.autoClassification.code': 'Code:',
    'action.autoClassification.codeTypes': 'js, py, java, cpp...',
    'action.autoClassificationDesc': 'System will automatically create corresponding classification folders under target path',
    'action.pathPreview': 'Path preview example:',
    'action.pathPreview.imageFiles': 'Image files → {path}/Images/',
    'action.pathPreview.documentFiles': 'Document files → {path}/Documents/',
    'action.pathPreview.videoFiles': 'Video files → {path}/Videos/',
    'action.pathPreview.otherFiles': 'Other files → {path}/Others/',
    'action.pathPreview.allFiles': 'All files → {path}/',
    'action.namingRule': 'Naming Rule',
    'action.naming.original': 'Keep Original',
    'action.naming.timestamp': 'Timestamp_Original',
    'action.naming.date': 'Date_Original',
    'action.naming.fileCreated': 'File Created Date_Original',
    'action.naming.fileModified': 'File Modified Date_Original',
    'action.naming.counter': 'Counter_Original',
    'action.naming.prefix': 'Add Prefix',
    'action.naming.suffix': 'Add Suffix',
    'action.naming.replace': 'Find & Replace',
    'action.naming.case': 'Case Conversion',
    'action.naming.custom': 'Custom Pattern',
    'action.naming.advanced': 'Advanced Combination',
    'action.dateFormat': 'Date Format',
    'action.customPattern': 'Custom Pattern',
    'action.customPatternPlaceholder': 'e.g.: {date}_{name} or IMG_{counter}',
    'action.availableVariables': 'Available variables:',
    'action.variable.name': '{name} - Original filename (without extension)',
    'action.variable.ext': '{ext} - File extension',
    'action.variable.date': '{date} - Current date (YYYY-MM-DD)',
    'action.variable.time': '{time} - Current time (HH-MM-SS)',
    'action.variable.counter': '{counter} - Auto-increment counter',
    'action.variable.type': '{type} - File type classification',
    'action.variable.year': '{year} - Year (2024)',
    'action.variable.month': '{month} - Month (01-12)',
    'action.variable.day': '{day} - Day (01-31)',
    'action.variable.hour': '{hour} - Hour (00-23)',
    'action.variable.minute': '{minute} - Minute (00-59)',
    'action.variable.second': '{second} - Second (00-59)',
    'action.examplePreview': 'Example preview:',
    'action.previewError': 'Preview error',
    'action.counterStart': 'Start Counter',
    'action.counterDigits': 'Counter Digits',
    'action.prefixContent': 'Prefix Content',
    'action.prefixPlaceholder': 'e.g.: ProjectA_',
    'action.prefixExample': 'Example: "ProjectA_" + "report.docx" = "ProjectA_report.docx"',
    'action.suffixContent': 'Suffix Content',
    'action.suffixPlaceholder': 'e.g.: _backup',
    'action.suffixExample': 'Example: "report" + "_backup" + ".docx" = "report_backup.docx"',
    'action.replaceFrom': 'Find',
    'action.replaceFromPlaceholder': 'Text to replace',
    'action.replaceTo': 'Replace With',
    'action.replaceToPlaceholder': 'New text (can be empty)',
    'action.replaceExample': 'Example: Find "IMG" Replace with "Photo" → "IMG_001.jpg" = "Photo_001.jpg"',
    'action.caseType': 'Conversion Type',
    'action.case.lower': 'All Lowercase',
    'action.case.upper': 'All Uppercase',
    'action.case.title': 'Title Case',
    'action.case.camel': 'Camel Case',
    'action.case.pascal': 'Pascal Case',
    'action.case.snake': 'Snake Case',
    'action.case.kebab': 'Kebab Case',
    'action.removeSpaces': 'Remove Spaces',
    'action.removeSpecialChars': 'Remove Special Characters',
    'action.caseExample': 'Example: "My File Name.txt" → "my_file_name.txt" (lowercase + underscore)',
    'action.advancedRules': 'Combination Rules',
    'action.addRule': 'Add Rule',
    'action.ruleType': 'Type',
    'action.ruleValue': 'Value',
    'action.ruleValuePlaceholder': 'Rule value',
    'action.advancedRuleTypes.prefix': 'Prefix',
    'action.advancedRuleTypes.suffix': 'Suffix',
    'action.advancedRuleTypes.replace': 'Replace',
    'action.advancedRuleTypes.case': 'Case',
    'action.advancedRuleTypes.counter': 'Counter',
    'action.advancedRuleTypes.date': 'Date',
    'action.advancedRuleTypes.custom': 'Custom',
    'action.advancedRulesEmpty': 'Click "Add Rule" to start creating combination renaming rules',
    'action.advancedRulesEmptyDesc': 'Rules will be executed in order',
    'action.deleteRecycleTip': 'Files can be recovered from recycle bin',
    'action.deleteRecycleTipDesc': 'Files will be moved to system recycle bin and can be restored anytime',
    'action.deleteEmptyFolders': 'Delete Empty Folders',
    'action.deleteNonEmptyFolders': 'Delete Non-Empty Folders (Dangerous)',
    'action.preserveFolderStructure': 'Preserve Folder Structure',
    'action.preserveFolderStructureDesc': 'Maintain original folder hierarchy when processing files',
    'action.conflictWarning': 'Preserve structure and file type classification cannot be enabled simultaneously',
    'action.mutuallyExclusiveNote': 'This option is mutually exclusive with "Preserve Folder Structure"/"Auto-classify by File Type", checking this will automatically uncheck the other',
    'action.selectProcessTargetFirst': 'Please select process target in basic settings first',
    'action.actionType': 'Action Type',
    'action.pathConfig': 'Path Configuration',
    'action.namingConfig': 'Naming Rule Configuration',
    'action.addAction': 'Add Process Action',
    'action.noActions': 'No process actions',
    'action.noActionsDesc': 'Add actions to define how files should be processed',
    // File processing action configuration guide
    'action.configGuide.files': 'File Processing Action Configuration Guide',
    'action.guide.files.classification': 'Auto-classification: Enable "Auto-classify by file type", system will intelligently create classification folders',
    'action.guide.files.naming': 'Flexible naming rules: Support timestamp, counter, custom patterns and other file naming methods',
    'action.guide.files.filtering': 'Precise filtering: Combine file size, type, modification time and other conditions for accurate processing',
    'action.guide.files.preview': 'Preview effects: After configuration, you can preview actual processing effects in workspace',

    // Folder processing action configuration guide
    'action.configGuide.folders': 'Folder Processing Action Configuration Guide',
    'action.guide.folders.organization': 'Folder organization: Move, copy folders to specified locations for classification management',
    'action.guide.folders.cleanup': 'Clean empty folders: Automatically detect and delete empty folders to keep directory structure tidy',
    'action.guide.folders.structure': 'Structure optimization: Adjust structure based on folder size, depth, content and other conditions',
    'action.guide.folders.preview': 'Preview effects: After configuration, you can preview actual processing effects in workspace',
    'action.dateFormatLabel': 'Date Format',
    'action.counterStartLabel': 'Start Counter',
    'action.counterDigitsLabel': 'Counter Digits',
    'action.prefixContentLabel': 'Prefix Content',
    'action.suffixContentLabel': 'Suffix Content',
    'action.replaceFromLabel': 'Find',
    'action.replaceToLabel': 'Replace With',
    'action.caseTypeLabel': 'Conversion Type',
    'action.removeSpacesLabel': 'Remove Spaces',
    'action.removeSpecialCharsLabel': 'Remove Special Characters',
    'action.advancedRulesLabel': 'Combination Rules',
    'action.addRuleButton': 'Add Rule',
    'action.replaceExampleText': 'Example: Find "IMG" Replace with "Photo" → "IMG_001.jpg" = "Photo_001.jpg"',
    'action.caseExampleText': 'Example: "My File Name.txt" → "my_file_name.txt" (lowercase + underscore)',
    'action.filterRulesTitle': 'Filter Rules',
    'action.filterRulesDesc': 'Set filtering conditions for files or folders',
    'action.filterInstructions': 'Filter Condition Instructions:',
    'action.noConditionsMatch': 'No conditions = match all files',
    'action.withConditionsMatch': 'With conditions, only files meeting criteria will be processed',
    'action.multipleConditions': 'Multiple conditions can be combined for precise filtering',
    'action.availableVariablesTitle': 'Available variables:',
    'action.examplePreviewTitle': 'Example preview:',
    'action.originalFile': 'Original file:',
    'action.newFile': 'New file:',
    'action.canAddMultipleActions': 'Multiple actions can be added and will be executed in order',
    'action.onlyMatchingItemsProcessed': 'Only items meeting conditions will be processed, if no conditions are set all items will be processed',
    'action.customModeTitle': 'Custom Mode',
    'action.counterStartNumber': 'Counter Start Number',
    'action.counterDigitsNumber': 'Counter Digits',
    'action.counterExample': 'Example: Start=101, Digits=3 → 101, 102, 103...',

    // 监控界面扩展
    'monitor.loading': 'Loading monitor tasks...',
    'monitor.fileWatch': 'File Watch',
    'monitor.scheduledTask': 'Scheduled Task',
    'monitor.associatedRule': 'Associated rule:',
    'monitor.executionCount': 'Execution count:',
    'monitor.nextExecution': 'Next execution:',
    'monitor.lastExecution': 'Last execution:',
    'monitor.expired': 'Expired',
    'monitor.aboutToExecute': 'About to execute',
    'monitor.minutesLater': '{minutes} minutes later',
    'monitor.hoursLater': '{hours} hours later',
    'monitor.daysLater': '{days} days later',
    'monitor.unknownRule': 'Unknown rule',
    'monitor.never': 'Never executed',

    // 错误消息
    'error.loadMonitorDataFailed': 'Failed to load monitor data:',
    'error.loadTaskStatusFailed': 'Failed to load task status:',
    'error.toggleTaskStatusFailed': 'Failed to toggle task status:',
    'error.deleteTaskFailed': 'Failed to delete task:',
    'error.executeTaskFailed': 'Failed to execute task:',
    'error.createTaskFailed': 'Failed to create task:',
    'error.sameSourceDestination': 'Source and destination must not be the same',
    'error.updateTaskFailed': 'Failed to update task:',
    'error.createRuleFailed': 'Failed to create rule:',
    'error.saveStepFailed': 'Failed to save step:',
    'error.addStepFailed': 'Failed to add step:',
    'error.toggleStepStatusFailed': 'Failed to toggle step status:',
    'error.saveRuleFailed': 'Failed to save rule:',
    'error.saveRuleOrderFailed': 'Failed to save rule order:',
    'error.saveStepOrderFailed': 'Failed to save step order:',
    'error.deleteStepFailed': 'Failed to delete step:',
    'error.resetFailed': 'Failed to reset:',
    'error.selectPathFailed': 'Failed to select path:',
    'error.selectFilesFailed': 'Failed to select files:',
    'error.selectFolderFailed': 'Failed to select folder:',

    // 成功消息
    'success.taskExecutionCompleted': 'Task execution completed:',
    'success.updateTaskData': 'Update only related task data, avoid full page refresh',
    'success.getUpdatedTaskData': 'Get updated task data',
    'success.updateTaskStatus': 'Update task status',
    'success.updateThisTaskOnly': 'Update only this task data',
    'success.reloadData': 'Reload data',
    'success.reloadDataSync': 'Reload data to ensure status synchronization',
    'success.updateLocalState': 'Update local state directly, avoid reloading entire list',
    'success.updateLocalStateNoRefresh': 'Update local state directly, no full list refresh',
    'success.closeFloatingEditor': 'Close floating editor',
    'success.updateOrderField': 'Update order field',
    'success.saveToBackend': 'Save to backend',
    'success.updateRule': 'Update rule, add new step',
    'success.clearEditState': 'Clear edit state',
    'success.checkCompatibility': 'Check if current operator is compatible with new field type',
    'success.updateFieldAndOperator': 'Update both field and operator (if needed)',
    'success.avoidDuplicateAdd': 'Check if task with same ID already exists, avoid duplicate addition',

    // 工作区界面
    'workspace.title': 'Workspace',
    'workspace.selectRule': 'Select Rule',
    'workspace.selectRulePlaceholder': 'Please select a rule to execute',
    'workspace.noRules': 'No rules available',
    'workspace.noRulesDesc': 'Please create and enable rules in Rule Center first',
    'workspace.dragDropFiles': 'Drag files here',
    'workspace.dragDropFilesDesc': 'or click to select files',
    'workspace.selectFiles': 'Select Files',
    'workspace.selectedFiles': '{count} files selected',
    'workspace.selectedFolders': '{count} folders selected',
    'workspace.selectedFilesAndFolders': '{fileCount} files and {folderCount} folders selected',
    'workspace.items': 'items',
    'workspace.folder': 'folder',
    'workspace.folders': 'folders',
    'workspace.files': 'files',
    'workspace.emptyFolder': 'empty folder',
    'workspace.noFilesSelected': 'No files selected',
    'workspace.preview': 'Preview',
    'workspace.execute': 'Execute',
    'workspace.cancel': 'Cancel',
    'workspace.processing': 'Processing...',
    'workspace.completed': 'Completed',
    'workspace.failed': 'Failed',
    'workspace.progress': 'Progress: {current}/{total}',
    'workspace.currentFile': 'Current file: {name}',
    'workspace.results': 'Results',
    'workspace.previewResults': 'Preview Results',
    'workspace.executionResults': 'Execution Results',
    'workspace.successCount': 'Success: {count}',
    'workspace.failedCount': 'Failed: {count}',
    'workspace.skippedCount': 'Skipped: {count}',
    'workspace.totalProcessed': 'Total processed: {count} files',
    'workspace.viewDetails': 'View Details',
    'workspace.hideDetails': 'Hide Details',
    'workspace.fileList': 'File List',
    'workspace.fileName': 'File Name',
    'workspace.filePath': 'Path',
    'workspace.fileSize': 'Size',
    'workspace.fileType': 'Type',
    'workspace.status': 'Status',
    'workspace.status.success': 'Success',
    'workspace.status.failed': 'Failed',
    'workspace.status.skipped': 'Skipped',
    'workspace.status.pending': 'Pending',
    'workspace.newPath': 'New Path',
    'workspace.error': 'Error',
    'workspace.clearFiles': 'Clear Files',
    'workspace.refreshRules': 'Refresh Rules',
    'workspace.loadingRules': 'Loading rules...',
    'workspace.loadingFiles': 'Loading files...',
    'workspace.dragActive': 'Release to add files',
    'workspace.supportedFormats': 'All file formats supported',
    'workspace.maxFileSize': 'Max file size {size}',
    'workspace.confirmCancel': 'Confirm Cancel',
    'workspace.confirmCancelDesc': 'Are you sure you want to cancel current processing? Processed files will not be rolled back.',
    'workspace.confirmClear': 'Confirm Clear',
    'workspace.confirmClearDesc': 'Are you sure you want to clear all files?',
    'workspace.oneClickMode': 'One-Click Execution Mode',
    'workspace.oneClickModeDesc': 'This rule will automatically process: {path}',
    'workspace.oneClickModeHint': 'Click execute to start organizing',
    'workspace.dragDropArea': 'Drag files or folders here',
    'workspace.pleaseSelectRule': 'Please select a rule first',
    'workspace.selectFilesButton': 'Select Files',
    'workspace.selectFolderButton': 'Select Folder',
    'workspace.noExtension': 'No extension',
    'workspace.noFiles': 'No files',
    'workspace.dragToStart': 'Drag files to the left area to start',
    'workspace.fileCount': '{count} files',
    'workspace.selectFilesTab': 'Select Files',
    'workspace.fileListTab': 'File List',
    'workspace.successLabel': 'Success:',
    'workspace.durationLabel': 'Duration:',
    'workspace.errorLabel': 'Errors:',
    'workspace.errorCount': '{count}',
    'workspace.executionStats': 'Execution Statistics',
    'workspace.originalPath': 'Original path:',
    'workspace.originalName': 'Original name:',
    'workspace.totalFilesLabel': 'Total:',
    'workspace.processedFilesLabel': 'Processed:',
    'workspace.errorCountLabel': 'Errors:',
    'workspace.stepCountLabel': 'Steps:',
    'workspace.newPathArrow': '→',
    'workspace.noChange': 'No change',
    'workspace.batchSelectSupport': 'Batch select • Auto detect',
    'workspace.noFilesTitle': 'No Files',
    'workspace.noFilesDesc': 'Please select files or folders to process first',
    'workspace.preparingProcess': 'Preparing to process...',
    'workspace.executionCompleteTitle': 'Execution Complete',
    'workspace.executionCompleteDesc': 'Rule execution completed! Processed {count} files',
    'workspace.processingFiles': '🔄 Organizing files...',
    'workspace.errorPrefix': 'Error: ',
    'workspace.totalFilesShort': 'Total:',
    'workspace.processedShort': 'Processed:',
    'workspace.errorsShort': 'Errors:',
    'workspace.stepsShort': 'Steps:',
    'workspace.currentLocation': 'Current location: ',
    'workspace.moreFilesCount': '{count} more files...',
    'workspace.stepExecutionDetails': 'Step Execution Details',
    'workspace.stepNumber': 'Step {number}: {name}',
    'workspace.inputFiles': 'Input: {count} files',
    'workspace.outputFiles': 'Output: {count} files',
    'workspace.matchedFiles': 'Matched: {count} files',
    'workspace.input': 'Input',
    'workspace.output': 'Output',
    'workspace.processed': 'Processed',
    'workspace.errorDetails': 'Error Details',
    'workspace.processingFile': 'Processing: {name}',
    'workspace.processingBatch': 'Processing batch {current}/{total}',
    'workspace.cancelProcessing': 'Cancel Processing',
    'workspace.previewFailed': 'Preview failed, please check rule configuration',
    'workspace.loadDefaultFilesFailed': 'Failed to load default path files:',
    'workspace.loadFailed': 'Load Failed',
    'workspace.loadFailedDesc': 'Unable to load files from path {path}',
    'workspace.executionFailed': 'Execution Failed',
    'workspace.executionFailedDesc': 'Execution failed, please check rule configuration and file permissions',
    'workspace.noPreview': 'No preview',
    'workspace.clickPreview': 'Click "Preview" to view effects',
    'workspace.previewChanges': 'Preview Changes',
    'workspace.fileLimitExceeded': 'File Limit Exceeded',
    'workspace.fileLimitExceededDesc': 'Currently have {{current}} files, trying to add {{adding}} files, total {{total}}, exceeds the limit of {{limit}}. Please reduce file count or adjust the limit in settings.',
    'workspace.pathInfo': 'Path Information',
    'workspace.rootPath': 'Root Path',
    'workspace.inputMismatchWarning': 'Input Files May Not Match',
    'workspace.detectedIssues': 'Detected Issues',
    'workspace.continuePreview': 'Continue Preview?',
    'workspace.fileProcessingFailed': 'File Processing Failed',
    'workspace.fileProcessingFailedDesc': 'An error occurred while processing files. Please check if files are accessible or try again.',
    'workspace.noMatchingFiles': 'No Matching Files',
    'workspace.workflowStepMismatch': 'Current workflow steps do not match input files',
    'workspace.noProcessingResult': 'No Processing Result',
    'workspace.workflowCompleteNoFiles': 'Workflow completed but no files were processed',
    'workspace.selectedCount': 'Selected {count} items',
    'workspace.selectedCountPending': 'Selected {count} items pending',
    'workspace.fileCountUnit': '{count} files',
    'workspace.folderCountUnit': '{count} folders',
    'workspace.limitInfo': 'Limit {limit}',
    'workspace.none': 'None',
    'workspace.itemsUnit': 'items',
    'workspace.moreChanges': '{count} more file changes below',
    'workspace.cannotProcessInputType': 'Workflow cannot process current input file types',
    'workspace.stepNeedsFiles': 'Step "{stepName}" needs files, but input contains only folders',
    'workspace.stepNeedsFolders': 'Step "{stepName}" needs folders, but input contains only files',
    'workspace.stepSummary': 'Step {stepNumber}: {stepName} ({itemCount} items)',
    'workspace.withErrors': ', {errorCount} failed',
    'workspace.detailsLabel': 'Details:',

    // 文件类型不匹配相关翻译
    'workspace.fileTypeMismatch': 'File Type Mismatch',
    'workspace.onlyFilesUploaded': 'You uploaded {fileCount} files',
    'workspace.onlyFoldersUploaded': 'You uploaded {folderCount} folders',
    'workspace.workflowNeedsFolders': 'but the current workflow can only process folders. Please select folders or switch to a workflow that supports files.',
    'workspace.workflowNeedsFiles': 'but the current workflow can only process files. Please select files or switch to a workflow that supports folders.',
    'workspace.switchWorkflowOrAddMatchingFiles': 'Please switch workflow or add matching file types',
    'workspace.workflowCannotProcessInput': 'Current workflow cannot process your selected input',
    'workspace.checkWorkflowTargetHint': 'Please check the workflow\'s processing target settings or select matching file types',

    // 历史记录界面
    'history.title': 'History',
    'history.searchPlaceholder': 'Search by filename or rule...',
    'history.refresh': 'Refresh',
    'history.clear': 'Clear',
    'history.loading': 'Loading history...',
    'history.noHistory': 'No history records',
    'history.noHistoryDesc': 'Operation history will appear here after executing rules',
    'history.actions': 'Actions',
    'history.time': 'Time',
    'history.workflow': 'Rule',
    'history.fileCount': 'Files',
    'history.result': 'Result',
    'history.status': 'Status',
    'history.workflowInfo': 'Rule: {name} • {source}',
    'history.source.manual': 'Manual',
    'history.source.fileWatch': 'File Watch',
    'history.source.scheduled': 'Scheduled',
    'history.monitorTask': 'Monitor Task: {name}',
    'history.processLog': 'Process Log',
    'history.undoTime': 'Undone at: {time}',
    'history.undone': 'Undone',
    'history.totalFiles': 'Total: {count} files',
    'history.processedFiles': 'Processed: {count}',
    'history.deleted': 'Deleted',
    'history.moreFiles': '{count} more files...',
    'history.undo': 'Undo',
    'history.redo': 'Redo',
    'history.failed': 'Failed',
    'history.partialSuccess': 'Partial Success',
    'history.completed': 'Completed',
    'history.errors': '{count} errors',
    'history.statistics': 'Statistics',
    'history.totalFileCount': 'Total Files',
    'history.successCount': 'Successfully Processed',
    'history.failedCount': 'Failed to Process',
    'history.duration': 'Duration',
    'history.fileOperations': 'File Operation Details',
    'history.currentLocation': 'Current location:',
    'history.originalLocation': 'Original location:',
    'history.statusUndone': 'Undone to original location',
    'history.originalPath': 'Original path:',
    'history.newPath': 'New path:',
    'history.operationType': 'Operation type:',
    'history.errorInfo': 'Error info:',
    'history.statusSuccess': 'Success',
    'history.statusError': 'Failed',
    'history.statusProcessing': 'Processing',
    'history.statusEmptyFolderCleaned': 'Empty folder cleaned',
    'history.statusEmptyFolderRestored': 'Empty folder restored',
    'history.errorSummary': 'Error Summary',
    'history.step': 'Step: {name}',
    'history.clearConfirmTitle': 'Clear History',
    'history.clearConfirmDesc': 'Are you sure you want to clear all history records? This action cannot be undone.',
    'history.deleteConfirmTitle': 'Delete History Record',
    'history.deleteConfirmDesc': 'Are you sure you want to delete this history record?',
    'history.undoSuccessTitle': 'Undo Successful',
    'history.undoSuccessDesc': 'File operation has been successfully undone',
    'history.undoFailedTitle': 'Undo Failed',
    'history.undoFailedDesc': 'Undo operation failed, file may have been moved or deleted',
    'history.undoFailedGeneric': 'Undo operation failed, please check file status',
    'history.redoSuccessTitle': 'Redo Successful',
    'history.redoSuccessDesc': 'File operation has been successfully redone',
    'history.redoFailedTitle': 'Redo Failed',
    'history.redoFailedDesc': 'Redo operation failed, file may have been moved or deleted',
    'history.redoFailedGeneric': 'Redo operation failed, please check file status',
  }
}

// 创建语言上下文
const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// 语言提供者组件
interface LanguageProviderProps {
  children: ReactNode
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguageState] = useState<Language>('zh-CN')

  // 从electron-store加载语言设置
  useEffect(() => {
    const loadLanguageSettings = async () => {
      if (window.electronAPI) {
        try {
          console.log('开始加载语言设置');
          const savedLanguage = await window.electronAPI.getSetting('language') as Language
          console.log('加载的语言设置:', savedLanguage);

          if (savedLanguage && (savedLanguage === 'zh-CN' || savedLanguage === 'en-US')) {
            console.log('应用保存的语言:', savedLanguage);
            setLanguageState(savedLanguage)

            // 应用启动时也更新默认工作流语言
            // 使用 AbortController 来处理组件卸载时的清理
            const abortController = new AbortController();
            window.electronAPI.updateDefaultWorkflowLanguage(savedLanguage)
              .then(() => {
                if (!abortController.signal.aborted) {
                  console.log('应用启动时更新默认工作流语言成功:', savedLanguage)
                }
              })
              .catch((error) => {
                if (!abortController.signal.aborted) {
                  console.error('应用启动时更新默认工作流语言失败:', error)
                }
              })

            // 返回清理函数
            return () => {
              abortController.abort();
            }
          } else {
            console.log('使用默认语言: zh-CN');
            // 如果没有保存的语言，设置默认语言并保存
            await window.electronAPI.setSetting('language', 'zh-CN');
          }
        } catch (error) {
          console.error('Failed to load language settings:', error)
        }
      }
    }

    loadLanguageSettings()
  }, [])

  // 设置语言并保存到electron-store
  const setLanguage = async (lang: Language) => {
    const previousLanguage = language
    console.log('设置新语言:', lang);
    setLanguageState(lang)

    // 保存到electron-store
    if (window.electronAPI) {
      try {
        console.log('保存语言设置到 electron-store:', lang);
        const result = await window.electronAPI.setSetting('language', lang)
        console.log('语言设置保存结果:', result);
      } catch (error) {
        console.error('Failed to save language setting:', error)
      }
    }

    // 注意：不在这里更新默认工作流语言，让各个组件自己处理
    // 这样可以避免重复调用和时序问题
  }

  // 翻译函数
  const t = (key: string, params?: Record<string, any>): string => {
    let text = (translations[language] as any)[key] || key

    // 如果有参数，进行替换
    if (params) {
      Object.keys(params).forEach(param => {
        text = text.replace(new RegExp(`\\{${param}\\}`, 'g'), params[param])
      })
    }

    return text
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}

// 使用语言上下文的Hook
export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}
