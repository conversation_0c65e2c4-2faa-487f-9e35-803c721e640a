"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// src/main/preload.ts
const electron_1 = require("electron");
// 定义我们将要暴露给前端的 API
const electronAPI = {
    openFile: () => electron_1.ipcRenderer.invoke('dialog:openFile'),
    openDirectory: () => electron_1.ipcRenderer.invoke('dialog:openDirectory'),
    processDroppedPaths: (paths, workflowId) => electron_1.ipcRenderer.invoke('files:processDroppedPaths', paths, workflowId),
    // 工作流操作
    previewWorkflow: (files, workflow) => electron_1.ipcRenderer.invoke('workflows:preview', files, workflow),
    executeWorkflow: (files, workflow) => electron_1.ipcRenderer.invoke('workflows:execute', files, workflow),
    // 工作流 CRUD 操作
    getAllWorkflows: () => electron_1.ipcRenderer.invoke('workflows:getAll'),
    saveWorkflow: (workflow) => electron_1.ipcRenderer.invoke('workflows:save', workflow),
    createWorkflow: (workflow) => electron_1.ipcRenderer.invoke('workflows:save', workflow),
    updateWorkflow: (workflow) => electron_1.ipcRenderer.invoke('workflows:save', workflow),
    deleteWorkflow: (workflowId) => electron_1.ipcRenderer.invoke('workflows:delete', workflowId),
    getWorkflowById: (workflowId) => electron_1.ipcRenderer.invoke('workflows:getById', workflowId),
    resetToDefaultWorkflows: (language) => electron_1.ipcRenderer.invoke('workflows:resetToDefault', language),
    updateDefaultWorkflowLanguage: (language) => electron_1.ipcRenderer.invoke('workflows:updateDefaultLanguage', language),
    // 历史记录操作
    getAllHistory: (limit, offset) => electron_1.ipcRenderer.invoke('history:getAll', limit, offset),
    getHistory: (limit, offset) => electron_1.ipcRenderer.invoke('history:getAll', limit, offset),
    searchHistory: (query, limit) => electron_1.ipcRenderer.invoke('history:search', query, limit),
    clearHistory: () => electron_1.ipcRenderer.invoke('history:clear'),
    deleteHistoryEntry: (entryId) => electron_1.ipcRenderer.invoke('history:delete', entryId),
    undoHistoryEntry: (entryId) => electron_1.ipcRenderer.invoke('history:undo', entryId),
    chainUndoHistoryEntry: (entryId) => electron_1.ipcRenderer.invoke('history:chainUndo', entryId),
    redoHistoryEntry: (entryId) => electron_1.ipcRenderer.invoke('history:redo', entryId),
    getHistoryStats: () => electron_1.ipcRenderer.invoke('history:getStats'),
    getHistorySettings: () => electron_1.ipcRenderer.invoke('history:getSettings'),
    updateHistorySettings: (settings) => electron_1.ipcRenderer.invoke('history:updateSettings', settings),
    // 监控功能操作
    getAllMonitorTasks: () => electron_1.ipcRenderer.invoke('monitor:getAllTasks'),
    getMonitorTask: (taskId) => electron_1.ipcRenderer.invoke('monitor:getTask', taskId),
    createMonitorTask: (taskData) => electron_1.ipcRenderer.invoke('monitor:createTask', taskData),
    updateMonitorTask: (taskId, updates) => electron_1.ipcRenderer.invoke('monitor:updateTask', taskId, updates),
    deleteMonitorTask: (taskId) => electron_1.ipcRenderer.invoke('monitor:deleteTask', taskId),
    executeMonitorTask: (taskId, filePaths) => electron_1.ipcRenderer.invoke('monitor:executeTask', taskId, filePaths),
    getMonitorTaskStatus: (taskId) => electron_1.ipcRenderer.invoke('monitor:getTaskStatus', taskId),
    getAllMonitorTaskStatuses: () => electron_1.ipcRenderer.invoke('monitor:getAllTaskStatuses'),
    // 监控事件监听
    onMonitorTaskCreated: (callback) => {
        electron_1.ipcRenderer.on('monitor:taskCreated', (_, task) => callback(task));
    },
    onMonitorTaskUpdated: (callback) => {
        electron_1.ipcRenderer.on('monitor:taskUpdated', (_, task) => callback(task));
    },
    onMonitorTaskDeleted: (callback) => {
        electron_1.ipcRenderer.on('monitor:taskDeleted', (_, taskId) => callback(taskId));
    },
    onMonitorExecutionCompleted: (callback) => {
        electron_1.ipcRenderer.on('monitor:executionCompleted', (_, data) => callback(data));
    },
    // 工作流进度监听
    onWorkflowProgress: (callback) => {
        electron_1.ipcRenderer.on('workflow-progress', (_, progress) => callback(progress));
    },
    onMonitorExecutionFailed: (callback) => {
        electron_1.ipcRenderer.on('monitor:executionFailed', (_, result) => callback(result));
    },
    onMonitorFilesDetected: (callback) => {
        electron_1.ipcRenderer.on('monitor:filesDetected', (_, data) => callback(data));
    },
    onMonitorTaskError: (callback) => {
        electron_1.ipcRenderer.on('monitor:taskError', (_, data) => callback(data));
    },
    // 获取设置
    getSetting: (key) => electron_1.ipcRenderer.invoke('settings:get', key),
    // 设置值
    setSetting: (key, value) => electron_1.ipcRenderer.invoke('settings:set', key, value),
    // 获取所有设置
    getAllSettings: () => electron_1.ipcRenderer.invoke('settings:getAll'),
    // 获取开机自启动状态
    getAutoLaunch: () => electron_1.ipcRenderer.invoke('auto-launch:get'),
    // 设置开机自启动状态
    setAutoLaunch: (enable) => electron_1.ipcRenderer.invoke('auto-launch:set', enable),
    // 存储管理
    getStorageUsage: () => electron_1.ipcRenderer.invoke('storage:getUsage'),
    cleanTempFiles: () => electron_1.ipcRenderer.invoke('storage:cleanTemp'),
    // 窗口控制
    minimizeWindow: () => electron_1.ipcRenderer.invoke('window:minimize'),
    maximizeWindow: () => electron_1.ipcRenderer.invoke('window:maximize'),
    closeWindow: () => electron_1.ipcRenderer.invoke('window:close'),
    isWindowMaximized: () => electron_1.ipcRenderer.invoke('window:isMaximized'),
    resetWindowToDefaultSize: () => electron_1.ipcRenderer.invoke('window:resetToDefaultSize'),
};
// 使用 contextBridge 将 API 安全地挂载到前端的 window 对象上
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
// ✅ 为了让 TypeScript 知道 window.electronAPI 的存在，我们需要扩展 Window 类型
// 在 renderer 进程中创建一个 .d.ts 文件来定义它
