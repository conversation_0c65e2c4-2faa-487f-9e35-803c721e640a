// 简单的测试脚本来验证复制操作的识别功能
// 这个测试模拟了工作流执行结果，验证历史记录管理器是否能正确识别 copy 操作

// 直接测试操作类型识别逻辑，不依赖 Electron 环境
function determineOperationType(inputFile, outputFile, workflowStep) {
  let operation = 'move';

  if (outputFile.path === '将被删除') {
    operation = 'delete';
  } else if (inputFile.path !== outputFile.path) {
    // 如果找到了对应的工作流步骤，使用步骤中的动作类型
    if (workflowStep && workflowStep.actions.length > 0) {
      // 获取第一个启用的动作类型
      const firstEnabledAction = workflowStep.actions.find(action => action.enabled);
      if (firstEnabledAction) {
        operation = firstEnabledAction.type;
      }
    } else {
      // 回退到基于路径的推断
      const path = require('path');
      const inputDir = path.dirname(inputFile.path);
      const outputDir = path.dirname(outputFile.path);

      if (inputDir === outputDir) {
        operation = 'rename';
      } else {
        operation = 'move';
      }
    }
  }

  return operation;
}

// 模拟数据
const mockWorkflowResult = {
  workflowId: 'test-workflow-1',
  stepResults: [
    {
      stepId: 'step-1',
      stepName: '复制文件',
      inputFiles: [
        {
          id: 'file-1',
          name: 'test.txt',
          path: 'C:\\source\\test.txt',
          size: 1024,
          type: 'txt',
          status: 'success'
        }
      ],
      outputFiles: [
        {
          id: 'file-1',
          name: 'test.txt',
          path: 'C:\\destination\\test.txt',
          size: 1024,
          type: 'txt',
          status: 'success'
        }
      ],
      processedCount: 1,
      errors: [],
      duration: 100
    }
  ],
  totalFiles: 1,
  processedFiles: 1,
  errors: [],
  startTime: new Date().toISOString(),
  endTime: new Date().toISOString(),
  duration: 100
};

const mockWorkflow = {
  id: 'test-workflow-1',
  name: '测试复制工作流',
  description: '用于测试复制操作识别的工作流',
  enabled: true,
  order: 1,
  steps: [
    {
      id: 'step-1',
      name: '复制文件',
      description: '复制文件到目标目录',
      enabled: true,
      order: 1,
      inputSource: { type: 'original' },
      conditions: { type: 'and', conditions: [] },
      actions: [
        {
          id: 'action-1',
          type: 'copy',
          enabled: true,
          order: 1,
          config: {
            targetPath: 'C:\\destination',
            createSubfolders: true
          }
        }
      ],
      processTarget: 'files'
    }
  ],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
};

const mockOriginalFiles = [
  {
    id: 'file-1',
    name: 'test.txt',
    path: 'C:\\source\\test.txt',
    size: 1024,
    type: 'txt',
    status: 'pending'
  }
];

// 测试函数
function testCopyOperationIdentification() {
  console.log('🧪 开始测试复制操作识别功能...');

  try {
    // 测试用例 1: 复制操作
    const inputFile1 = {
      path: 'C:\\source\\test.txt',
      name: 'test.txt'
    };
    const outputFile1 = {
      path: 'C:\\destination\\test.txt',
      name: 'test.txt'
    };
    const workflowStep1 = {
      actions: [
        {
          type: 'copy',
          enabled: true
        }
      ]
    };

    const operation1 = determineOperationType(inputFile1, outputFile1, workflowStep1);
    console.log(`测试用例 1 - 复制操作: ${operation1}`);

    // 测试用例 2: 移动操作
    const workflowStep2 = {
      actions: [
        {
          type: 'move',
          enabled: true
        }
      ]
    };

    const operation2 = determineOperationType(inputFile1, outputFile1, workflowStep2);
    console.log(`测试用例 2 - 移动操作: ${operation2}`);

    // 测试用例 3: 重命名操作
    const outputFile3 = {
      path: 'C:\\source\\test_renamed.txt',
      name: 'test_renamed.txt'
    };
    const workflowStep3 = {
      actions: [
        {
          type: 'rename',
          enabled: true
        }
      ]
    };

    const operation3 = determineOperationType(inputFile1, outputFile3, workflowStep3);
    console.log(`测试用例 3 - 重命名操作: ${operation3}`);

    // 验证结果
    const success = operation1 === 'copy' && operation2 === 'move' && operation3 === 'rename';

    if (success) {
      console.log('✅ 所有测试用例通过!');
    } else {
      console.log('❌ 部分测试用例失败');
    }

    return success;

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  const success = testCopyOperationIdentification();
  process.exit(success ? 0 : 1);
}

module.exports = { testCopyOperationIdentification };
